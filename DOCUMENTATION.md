# ASL Recognition System - Complete Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Installation Guide](#installation-guide)
3. [Quick Start](#quick-start)
4. [Detailed Usage](#detailed-usage)
5. [Architecture](#architecture)
6. [API Reference](#api-reference)
7. [Performance Optimization](#performance-optimization)
8. [Troubleshooting](#troubleshooting)

## System Overview

The ASL Recognition System is a comprehensive real-time American Sign Language recognition platform that provides:

- **Real-time Recognition**: 30 FPS processing with MediaPipe hand tracking
- **Custom Training**: Train models on your own collected data
- **Interactive Interface**: Streamlit web application for easy interaction
- **High Accuracy**: CNN-based models with >90% target accuracy
- **Modular Design**: Separate components for data collection, training, and inference

### Key Features
- Support for ASL alphabet (A-Z) recognition
- Real-time webcam processing with confidence scoring
- Data augmentation and preprocessing pipeline
- Transfer learning with pre-trained backbones
- Performance monitoring and analytics
- Cross-platform compatibility

## Installation Guide

### Prerequisites
- Python 3.8 or higher
- Webcam for data collection and testing
- 4GB+ RAM recommended
- GPU support optional but recommended for training

### Step-by-Step Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd "Sign Language Detector"
```

2. **Create virtual environment:**
```bash
python -m venv venv

# On Windows:
venv\Scripts\activate

# On macOS/Linux:
source venv/bin/activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Verify installation:**
```bash
python demo.py --mode info
```

## Quick Start

### Option 1: Streamlit Web Application (Recommended)
```bash
streamlit run app.py
```
Navigate through the web interface to collect data, train models, and test recognition.

### Option 2: Command Line Demo
```bash
# Run complete demo workflow
python demo.py --mode all

# Or run individual components
python demo.py --mode collect  # Data collection
python demo.py --mode train    # Model training
python demo.py --mode test     # Real-time testing
```

## Detailed Usage

### 1. Data Collection

#### Using Streamlit Interface:
1. Open the web app: `streamlit run app.py`
2. Navigate to "Data Collection" mode
3. Select ASL letter to collect
4. Set target number of samples (recommended: 100+ per letter)
5. Click "Start Data Collection"
6. Follow on-screen instructions in camera window

#### Using Python API:
```python
from src.data_collection import ASLDataCollector

collector = ASLDataCollector()
collector.collect_data_for_label('A', target_samples=100)
```

#### Data Collection Tips:
- Use consistent lighting conditions
- Vary hand positions and angles slightly
- Keep gestures clear and distinct
- Collect data from multiple sessions for better generalization

### 2. Model Training

#### Using Streamlit Interface:
1. Navigate to "Model Training" mode
2. Configure training parameters:
   - Model type (custom CNN or transfer learning)
   - Number of epochs (30-50 recommended)
   - Batch size (16-32 for most systems)
   - Data augmentation settings
3. Click "Start Training"
4. Monitor training progress and metrics

#### Using Python API:
```python
from src.training import ASLTrainer

trainer = ASLTrainer()
dataset = trainer.prepare_data(augment=True, augmentation_factor=3)
model = trainer.create_model(model_type='custom')
history = trainer.train_model(epochs=30, batch_size=32)
results = trainer.evaluate_model()
```

#### Training Configuration:
- **Custom CNN**: Train from scratch, longer training time, potentially higher accuracy
- **Transfer Learning**: Use pre-trained backbone, faster training, good for limited data
- **Data Augmentation**: Increases dataset size, improves generalization
- **Batch Size**: Adjust based on available memory (16-32 typical)

### 3. Real-time Testing

#### Using Streamlit Interface:
1. Navigate to "Real-time Testing" mode
2. Select trained model from dropdown
3. Adjust confidence threshold (0.8 recommended)
4. Click "Start Real-time Testing"
5. Show ASL gestures to camera

#### Using Python API:
```python
from src.inference import ASLInferenceEngine

engine = ASLInferenceEngine('data/models/best_model.h5', confidence_threshold=0.8)
engine.run_inference()
```

#### Testing Controls:
- **'q'**: Quit testing session
- **'r'**: Reset performance statistics
- **Confidence Threshold**: Minimum confidence for reliable predictions

## Architecture

### System Components

```
Sign Language Detector/
├── app.py                 # Streamlit web application
├── demo.py               # Command-line demo script
├── src/
│   ├── data_collection.py # Data collection with MediaPipe
│   ├── preprocessing.py   # Data preprocessing and augmentation
│   ├── model.py          # CNN model architectures
│   ├── training.py       # Training pipeline with callbacks
│   ├── inference.py      # Real-time inference engine
│   └── utils.py          # Utility functions
├── data/
│   ├── raw/              # Raw collected images (organized by letter)
│   ├── processed/        # Preprocessed training data
│   └── models/           # Saved model weights and metadata
└── requirements.txt      # Python dependencies
```

### Data Flow

1. **Collection**: MediaPipe detects hands → Extract hand regions → Save labeled images
2. **Preprocessing**: Load images → Augmentation → Train/val/test split → Normalization
3. **Training**: CNN architecture → Training loop → Validation → Model saving
4. **Inference**: Live video → Hand detection → Preprocessing → Model prediction → Display

### Model Architecture

#### Custom CNN:
- Input: 224×224×3 RGB images
- 4 Convolutional blocks with BatchNorm and Dropout
- Global Average Pooling
- 2 Dense layers with regularization
- Output: 26 classes (A-Z) with softmax

#### Transfer Learning:
- Base: MobileNetV2 or EfficientNetB0 (ImageNet pre-trained)
- Custom head: GAP → Dense layers → Classification
- Fine-tuning available for better performance

## API Reference

### ASLDataCollector
```python
collector = ASLDataCollector(data_dir="data/raw")
collector.collect_data_for_label(label, target_samples)
collector.get_collection_summary()
```

### ASLDataPreprocessor
```python
preprocessor = ASLDataPreprocessor()
dataset = preprocessor.prepare_dataset(test_size=0.2, augment=True)
preprocessor.save_processed_dataset(dataset)
```

### ASLModel
```python
model = ASLModel(input_shape=(224, 224, 3), num_classes=26)
compiled_model = model.create_model(model_type='custom')
prediction, confidence = model.predict_single_image(image, label_encoder)
```

### ASLTrainer
```python
trainer = ASLTrainer()
dataset = trainer.prepare_data()
model = trainer.create_model()
history = trainer.train_model(epochs=30)
results = trainer.evaluate_model()
```

### ASLInferenceEngine
```python
engine = ASLInferenceEngine(model_path, confidence_threshold=0.8)
engine.run_inference(camera_index=0)
```

## Performance Optimization

### Hardware Recommendations
- **CPU**: Multi-core processor (Intel i5+ or AMD Ryzen 5+)
- **RAM**: 8GB+ recommended for training
- **GPU**: NVIDIA GPU with CUDA support for faster training
- **Camera**: USB webcam with 720p+ resolution

### Software Optimizations
- Use GPU acceleration when available
- Optimize batch size based on available memory
- Use mixed precision training for faster convergence
- Enable camera hardware acceleration

### Real-time Performance Tips
- Reduce input resolution if needed (224×224 minimum)
- Use model quantization for faster inference
- Optimize MediaPipe settings for your hardware
- Close unnecessary applications during real-time testing

## Troubleshooting

### Common Issues

#### Camera Not Detected
```
Error: Could not open camera
```
**Solutions:**
- Check camera permissions
- Try different camera index (0, 1, 2...)
- Restart application
- Check if camera is used by another application

#### Insufficient Training Data
```
Warning: Insufficient training data (X samples)
```
**Solutions:**
- Collect at least 100 samples per letter
- Use data augmentation to increase dataset size
- Focus on letters with fewer samples

#### Low Model Accuracy
```
Test accuracy below expected threshold
```
**Solutions:**
- Collect more diverse training data
- Increase training epochs
- Try transfer learning approach
- Adjust data augmentation parameters

#### Memory Issues During Training
```
ResourceExhaustedError: OOM when allocating tensor
```
**Solutions:**
- Reduce batch size
- Use gradient accumulation
- Close other applications
- Use mixed precision training

#### Import Errors
```
ModuleNotFoundError: No module named 'src'
```
**Solutions:**
- Ensure you're in the correct directory
- Check Python path configuration
- Reinstall requirements: `pip install -r requirements.txt`

### Performance Monitoring

Monitor these metrics during operation:
- **FPS**: Target 30 FPS for real-time processing
- **Confidence**: Average confidence should be >0.8 for reliable predictions
- **Memory Usage**: Should remain stable during long sessions
- **CPU/GPU Utilization**: Optimize based on available resources

### Getting Help

1. Check this documentation first
2. Review error messages and logs
3. Verify system requirements
4. Test with demo script: `python demo.py --mode info`
5. Check camera and dependencies with system info

For additional support, ensure you have:
- System specifications
- Error messages and logs
- Steps to reproduce the issue
- Python and package versions
