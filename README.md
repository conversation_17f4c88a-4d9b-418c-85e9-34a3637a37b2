# Sign Language Recognition System

A comprehensive real-time American Sign Language (ASL) recognition system with training and testing capabilities.

## Features

- **Real-time ASL Recognition**: Detect and classify hand gestures for ASL alphabets (A-Z)
- **Training Mode**: Collect and label training data from webcam
- **Testing Mode**: Real-time gesture recognition with confidence scores
- **Interactive Web Interface**: Streamlit-based GUI for easy interaction
- **High Performance**: 30 FPS processing with MediaPipe hand tracking

## System Requirements

- Python 3.8+
- Webcam for data collection and testing
- Minimum 4GB RAM recommended
- GPU support optional but recommended for training

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd "Sign Language Detector"
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Running the Application

```bash
streamlit run app.py
```

### Training Mode
1. Select "Training Mode" in the Streamlit interface
2. Choose the ASL letter to collect data for
3. Position your hand in front of the camera
4. Collect training samples (recommended: 100+ per letter)
5. Train the model using the collected data

### Testing Mode
1. Select "Testing Mode" in the Streamlit interface
2. Load a pre-trained model
3. Show ASL gestures to the camera
4. View real-time predictions with confidence scores

## Project Structure

```
Sign Language Detector/
├── app.py                 # Main Streamlit application
├── src/
│   ├── data_collection.py # Data collection module
│   ├── preprocessing.py   # Data preprocessing pipeline
│   ├── model.py          # CNN model architecture
│   ├── training.py       # Model training pipeline
│   ├── inference.py      # Real-time inference engine
│   └── utils.py          # Utility functions
├── data/
│   ├── raw/              # Raw collected images
│   ├── processed/        # Preprocessed training data
│   └── models/           # Saved model weights
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Model Architecture

- **Input**: 224x224 RGB images of hand gestures
- **Architecture**: CNN with spatial feature extraction
- **Output**: 26 classes (ASL alphabet A-Z)
- **Confidence Threshold**: 80% for reliable predictions

## Performance Metrics

- **Frame Rate**: 30 FPS real-time processing
- **Accuracy**: Target >90% on validation set
- **Inference Time**: <33ms per frame

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
