"""
Streamlit Web Application for ASL Sign Language Recognition System
Interactive dashboard with training and testing modes
"""

import streamlit as st
import cv2
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
import json
import time
from datetime import datetime
import threading
import queue

# Import custom modules
from src.data_collection import ASLDataCollector
from src.preprocessing import ASLDataPreprocessor
from src.training import ASLTrainer
from src.inference import ASLInferenceEngine
from src.utils import (
    get_asl_alphabet, get_collection_summary, get_available_models,
    validate_camera_access, print_system_info, get_external_dataset_summary,
    validate_external_dataset, get_external_dataset_classes, get_camera_info,
    find_working_camera
)


# Page configuration
st.set_page_config(
    page_title="ASL Recognition System",
    page_icon="🤟",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #c3e6cb;
    }
    .warning-message {
        background-color: #fff3cd;
        color: #856404;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #ffeaa7;
    }
</style>
""", unsafe_allow_html=True)


def main():
    """Main application function"""
    
    # Header
    st.markdown('<h1 class="main-header">🤟 ASL Recognition System</h1>', unsafe_allow_html=True)
    
    # Sidebar navigation
    st.sidebar.title("Navigation")
    mode = st.sidebar.selectbox(
        "Select Mode",
        ["Home", "Data Collection", "Model Training", "Real-time Testing", "System Info"]
    )

    # Quick camera status in sidebar
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📹 Quick Status")

    camera_info = get_camera_info()
    if camera_info['camera_accessible']:
        st.sidebar.success(f"✅ Camera {camera_info['working_camera_index']}")

        # Quick test button
        if st.sidebar.button("🚀 Quick Camera Test"):
            st.sidebar.info("Check the main area for test results!")
            st.session_state.quick_camera_test = True
    else:
        st.sidebar.error("❌ Camera Issue")
        if st.sidebar.button("🔧 Troubleshoot"):
            st.sidebar.code("""
# Run in terminal:
python3 camera_test_tool.py
            """)

    # Model status
    available_models = get_available_models()
    if available_models:
        st.sidebar.info(f"🧠 {len(available_models)} model(s) ready")
    else:
        st.sidebar.warning("⚠️ No trained models")
    
    # Route to different pages
    if mode == "Home":
        show_home_page()
    elif mode == "Data Collection":
        show_data_collection_page()
    elif mode == "Model Training":
        show_training_page()
    elif mode == "Real-time Testing":
        show_testing_page()
    elif mode == "System Info":
        show_system_info_page()

    # Handle quick camera test from sidebar
    if st.session_state.get('quick_camera_test', False):
        st.markdown("### 🚀 Quick Camera Test Results")

        try:
            import cv2
            import numpy as np

            # Test basic OpenCV window
            st.info("Testing OpenCV window display...")

            test_img = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.putText(test_img, 'Quick Test - Press any key', (120, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

            cv2.namedWindow('Quick Camera Test', cv2.WINDOW_NORMAL)
            cv2.imshow('Quick Camera Test', test_img)
            cv2.waitKey(3000)  # 3 second display
            cv2.destroyAllWindows()

            st.success("✅ OpenCV window test completed!")

            # Test camera access
            camera_info = get_camera_info()
            if camera_info['camera_accessible']:
                camera_index = camera_info['working_camera_index']

                st.info(f"Testing camera {camera_index} access...")

                cap = cv2.VideoCapture(camera_index)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        st.success(f"✅ Camera {camera_index} working! Frame shape: {frame.shape}")
                    else:
                        st.error(f"❌ Camera {camera_index} opened but no frame")
                    cap.release()
                else:
                    st.error(f"❌ Could not open camera {camera_index}")
            else:
                st.error("❌ No working camera found")

        except Exception as e:
            st.error(f"❌ Quick test failed: {e}")

        finally:
            st.session_state.quick_camera_test = False


def show_home_page():
    """Display home page with overview and statistics"""
    
    st.markdown("## Welcome to the ASL Recognition System")
    st.markdown("""
    This system provides comprehensive tools for American Sign Language (ASL) recognition:
    
    - **Data Collection**: Capture and label training images for ASL alphabet gestures
    - **Model Training**: Train custom CNN models on collected data
    - **Real-time Testing**: Test trained models with live webcam feed
    - **Performance Analytics**: Track model performance and training metrics
    """)
    
    # System status
    col1, col2, col3 = st.columns(3)
    
    with col1:
        camera_info = get_camera_info()
        if camera_info['camera_accessible']:
            camera_status = f"✅ Available (Index {camera_info['working_camera_index']})"
        else:
            camera_status = "❌ Not Available"
        st.metric("Camera Status", camera_status)
    
    with col2:
        data_summary = get_collection_summary()
        total_samples = sum(data_summary.values())
        st.metric("Total Training Samples", total_samples)
    
    with col3:
        available_models = get_available_models()
        st.metric("Available Models", len(available_models))
    
    # Data overview section
    st.markdown("### Dataset Overview")

    # Check for external dataset
    external_dataset_path = "/home/<USER>/Documents/Sign Language Detector/asl_dataset"
    external_validation = validate_external_dataset(external_dataset_path)
    external_valid = external_validation['valid']

    # Create tabs for different data sources
    tab1, tab2 = st.tabs(["Manual Collection", "External Dataset"])

    with tab1:
        if total_samples > 0:
            st.markdown("#### Manually Collected Data")

            # Create DataFrame for plotting
            df = pd.DataFrame(list(data_summary.items()), columns=['Letter', 'Samples'])

            # Plot data distribution
            fig = px.bar(df, x='Letter', y='Samples',
                        title='Manual Collection - Data Distribution by ASL Letter',
                        color='Samples', color_continuous_scale='viridis')
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)

            # Show statistics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Letters with Data", len([c for c in data_summary.values() if c > 0]))
            with col2:
                st.metric("Average per Letter", f"{total_samples / 26:.1f}")
            with col3:
                st.metric("Max Samples", max(data_summary.values()))
            with col4:
                st.metric("Min Samples", min(data_summary.values()))
        else:
            st.info("No manually collected data yet. Use the Data Collection page to start collecting.")

    with tab2:
        if external_valid:
            st.markdown("#### External Dataset")
            external_summary = get_external_dataset_summary(external_dataset_path)
            external_total = external_validation['total_images']
            external_classes = external_validation['total_classes']

            if external_total > 0:
                # Create DataFrame for plotting
                df_ext = pd.DataFrame(list(external_summary.items()), columns=['Class', 'Samples'])

                # Plot data distribution
                fig_ext = px.bar(df_ext, x='Class', y='Samples',
                            title=f'External Dataset - Data Distribution ({external_classes} classes)',
                            color='Samples', color_continuous_scale='blues')
                fig_ext.update_layout(height=400)
                st.plotly_chart(fig_ext, use_container_width=True)

                # Show statistics
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Total Classes", external_classes)
                with col2:
                    st.metric("Total Images", external_total)
                with col3:
                    st.metric("Max Samples", max(external_summary.values()) if external_summary else 0)
                with col4:
                    st.metric("Min Samples", min(external_summary.values()) if external_summary else 0)

                st.success(f"✅ External dataset validated: {external_total} images across {external_classes} classes")

                # Show any warnings
                if external_validation['warnings']:
                    with st.expander("⚠️ Dataset Warnings"):
                        for warning in external_validation['warnings']:
                            st.warning(warning)
            else:
                st.warning("External dataset found but no valid images detected.")
        else:
            st.error("❌ External dataset validation failed:")
            for error in external_validation['errors']:
                st.error(f"  • {error}")
            st.info(f"Expected dataset path: {external_dataset_path}")
    
    # Available models
    if available_models:
        st.markdown("### Available Models")
        
        models_df = pd.DataFrame(available_models)
        st.dataframe(
            models_df[['name', 'size_mb', 'created']].rename(columns={
                'name': 'Model Name',
                'size_mb': 'Size (MB)',
                'created': 'Created'
            }),
            use_container_width=True
        )


def show_data_collection_page():
    """Display data collection interface"""
    
    st.markdown("## 📸 Data Collection")
    st.markdown("Collect training images for ASL alphabet recognition.")

    # Info about external dataset
    external_dataset_path = "/home/<USER>/Documents/Sign Language Detector/asl_dataset"
    external_validation = validate_external_dataset(external_dataset_path)

    if external_validation['valid']:
        st.info(f"💡 **Alternative**: An external dataset with {external_validation['total_images']} images across {external_validation['total_classes']} classes is available. You can use it directly in the Model Training section instead of collecting data manually.")
    
    # Check camera availability
    camera_info = get_camera_info()
    if not camera_info['camera_accessible']:
        st.error("❌ Camera not available. Please check your camera connection.")
        st.info("💡 **Tip**: You can still train models using the external dataset in the Model Training section.")
        return
    else:
        st.success(f"✅ Camera detected at index {camera_info['working_camera_index']}")
    
    # Data collection settings
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.markdown("### Settings")
        
        selected_letter = st.selectbox(
            "Select ASL Letter",
            get_asl_alphabet()
        )
        
        target_samples = st.number_input(
            "Target Samples",
            min_value=10,
            max_value=500,
            value=100,
            step=10
        )
        
        # Current collection status
        data_summary = get_collection_summary()
        current_samples = data_summary.get(selected_letter, 0)
        
        st.info(f"Current samples for '{selected_letter}': {current_samples}")
        
        if st.button("Start Data Collection", type="primary"):
            st.session_state.collecting = True
    
    with col2:
        st.markdown("### Instructions")
        st.markdown("""
        1. **Position yourself** in front of the camera with good lighting
        2. **Select the ASL letter** you want to collect data for
        3. **Click 'Start Data Collection'** to begin
        4. **Show the gesture** clearly to the camera
        5. **Press 's'** to save each sample when your hand is positioned correctly
        6. **Press 'q'** to quit collection
        
        **Tips for better data quality:**
        - Use consistent lighting
        - Vary hand positions slightly
        - Keep gestures clear and distinct
        - Collect samples from different angles
        """)
    
    # Data collection execution
    if st.session_state.get('collecting', False):
        st.markdown("### Data Collection in Progress")
        st.info("A new window will open for data collection. Follow the on-screen instructions.")
        
        try:
            collector = ASLDataCollector()
            success = collector.collect_data_for_label(selected_letter, target_samples)
            
            if success:
                st.success(f"✅ Data collection completed for letter '{selected_letter}'!")
            else:
                st.error("❌ Data collection failed. Please try again.")
        
        except Exception as e:
            st.error(f"❌ Error during data collection: {str(e)}")
        
        finally:
            st.session_state.collecting = False
            st.experimental_rerun()
    
    # Show current data distribution
    st.markdown("### Data Distribution Overview")

    # Create tabs for different data sources
    tab1, tab2 = st.tabs(["Manual Collection", "External Dataset"])

    with tab1:
        manual_data_summary = get_collection_summary()
        manual_total = sum(manual_data_summary.values())

        if manual_total > 0:
            df = pd.DataFrame(list(manual_data_summary.items()), columns=['Letter', 'Samples'])
            fig = px.bar(df, x='Letter', y='Samples',
                        title='Manual Collection - Current Data Distribution',
                        color='Samples', color_continuous_scale='viridis')
            st.plotly_chart(fig, use_container_width=True)

            col1, col2 = st.columns(2)
            with col1:
                st.metric("Total Samples", manual_total)
            with col2:
                st.metric("Letters with Data", len([c for c in manual_data_summary.values() if c > 0]))
        else:
            st.info("No manually collected data yet. Use the controls above to start collecting!")

    with tab2:
        external_dataset_path = "/home/<USER>/Documents/Sign Language Detector/asl_dataset"
        external_validation = validate_external_dataset(external_dataset_path)
        external_valid = external_validation['valid']

        if external_valid:
            external_summary = get_external_dataset_summary(external_dataset_path)
            external_total = sum(external_summary.values())

            if external_total > 0:
                df_ext = pd.DataFrame(list(external_summary.items()), columns=['Letter', 'Samples'])
                fig_ext = px.bar(df_ext, x='Letter', y='Samples',
                            title='External Dataset - Data Distribution',
                            color='Samples', color_continuous_scale='blues')
                st.plotly_chart(fig_ext, use_container_width=True)

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Total Samples", external_total)
                with col2:
                    st.metric("Letters with Data", len([c for c in external_summary.values() if c > 0]))
                with col3:
                    st.metric("Dataset Path", "Available")

                st.success(f"✅ External dataset validated: {external_total} images")
            else:
                st.warning("External dataset found but no valid images detected.")
        else:
            st.error("❌ External dataset validation failed:")
            for error in external_validation['errors']:
                st.error(f"  • {error}")
            st.info(f"Expected dataset path: {external_dataset_path}")


def show_training_page():
    """Display model training interface"""

    st.markdown("## 🧠 Model Training")
    st.markdown("Train custom CNN models on ASL data.")

    # Dataset source selection
    st.markdown("### Dataset Source Selection")

    # Check available data sources
    manual_data_summary = get_collection_summary()
    manual_total = sum(manual_data_summary.values())

    external_dataset_path = "/home/<USER>/Documents/Sign Language Detector/asl_dataset"
    external_validation = validate_external_dataset(external_dataset_path)
    external_valid = external_validation['valid']
    external_summary = get_external_dataset_summary(external_dataset_path) if external_valid else {}
    external_total = sum(external_summary.values()) if external_valid else 0

    # Data source options
    data_source_options = []
    if manual_total > 0:
        data_source_options.append(f"Manual Collection ({manual_total} samples)")
    if external_valid and external_total > 0:
        data_source_options.append(f"External Dataset ({external_total} samples)")

    if not data_source_options:
        st.error("❌ No training data available. Please collect data manually or ensure external dataset is accessible.")
        return

    selected_source = st.selectbox(
        "Choose Data Source",
        data_source_options,
        help="Select whether to use manually collected data or external dataset for training"
    )

    # Determine actual data source and validate
    if "Manual Collection" in selected_source:
        data_source = "manual"
        total_samples = manual_total
        if total_samples < 50:
            st.warning(f"⚠️ Limited training data ({total_samples} samples). Consider collecting more data or using external dataset.")
    else:
        data_source = "external"
        total_samples = external_total
        if total_samples < 26:
            st.warning(f"⚠️ Limited training data ({total_samples} samples). Each letter should have multiple samples for better training.")

    st.info(f"Selected: {selected_source}")
    
    # Training configuration
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.markdown("### Training Configuration")
        
        model_type = st.selectbox(
            "Model Type",
            ["custom", "transfer"],
            help="Custom: Train from scratch, Transfer: Use pre-trained backbone"
        )
        
        if model_type == "transfer":
            base_model = st.selectbox(
                "Base Model",
                ["mobilenetv2", "efficientnetb0"]
            )
        
        epochs = st.number_input("Epochs", min_value=5, max_value=100, value=30)
        batch_size = st.number_input("Batch Size", min_value=8, max_value=64, value=32)
        
        augment_data = st.checkbox("Data Augmentation", value=True)
        if augment_data:
            aug_factor = st.number_input("Augmentation Factor", min_value=1, max_value=5, value=3)
        
        model_name = st.text_input(
            "Model Name",
            value=f"asl_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        if st.button("Start Training", type="primary"):
            st.session_state.training = True
    
    with col2:
        st.markdown("### Training Information")
        
        # Show data statistics based on selected source
        if data_source == "manual":
            current_summary = manual_data_summary
        else:
            current_summary = external_summary

        letters_with_data = len([c for c in current_summary.values() if c > 0])
        avg_samples = total_samples / 26

        st.markdown(f"""
        **Dataset Statistics:**
        - Data source: {data_source.title()}
        - Total samples: {total_samples}
        - Letters with data: {letters_with_data}/26
        - Average per letter: {avg_samples:.1f}

        **Training Process:**
        1. Data preprocessing and augmentation
        2. Model architecture creation
        3. Training with validation
        4. Model evaluation and saving

        **Estimated Training Time:**
        - Custom model: ~{epochs * 2} minutes
        - Transfer learning: ~{epochs * 1} minutes

        **Note:** {data_source.title()} dataset will be used for training.
        """)
    
    # Training execution
    if st.session_state.get('training', False):
        st.markdown("### Training in Progress")
        
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        try:
            # Initialize trainer
            trainer = ASLTrainer()
            
            # Prepare data
            status_text.text("Preparing training data...")
            if data_source == "external":
                dataset = trainer.prepare_data(
                    augment=augment_data,
                    augmentation_factor=aug_factor if augment_data else 1,
                    data_source="external",
                    external_dataset_path=external_dataset_path
                )
            else:
                dataset = trainer.prepare_data(
                    augment=augment_data,
                    augmentation_factor=aug_factor if augment_data else 1,
                    data_source="manual"
                )
            progress_bar.progress(0.2)
            
            # Create model
            status_text.text("Creating model architecture...")
            if model_type == "transfer":
                model = trainer.create_model(model_type="transfer", base_model_name=base_model)
            else:
                model = trainer.create_model(model_type="custom")
            progress_bar.progress(0.3)
            
            # Train model
            status_text.text("Training model...")
            history = trainer.train_model(
                epochs=epochs,
                batch_size=batch_size,
                model_name=model_name
            )
            progress_bar.progress(0.8)
            
            # Evaluate model
            status_text.text("Evaluating model...")
            results = trainer.evaluate_model()
            progress_bar.progress(1.0)
            
            # Show results
            st.success("✅ Training completed successfully!")
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Test Accuracy", f"{results['test_accuracy']:.3f}")
            with col2:
                st.metric("Test Loss", f"{results['test_loss']:.3f}")
            with col3:
                st.metric("Top-3 Accuracy", f"{results['test_top3_accuracy']:.3f}")
        
        except Exception as e:
            st.error(f"❌ Training failed: {str(e)}")
        
        finally:
            st.session_state.training = False


def show_testing_page():
    """Display real-time testing interface with enhanced camera support"""

    st.markdown("## 🎯 Real-time Testing")
    st.markdown("Test trained models with live webcam feed and real-time ASL recognition.")

    # Enhanced camera status with detailed information
    camera_info = get_camera_info()

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown("### 📹 Camera Status")
        if camera_info['camera_accessible']:
            st.success(f"✅ Camera Available")
            st.info(f"**Working Camera**: Index {camera_info['working_camera_index']}")

            # Show camera details
            for cam in camera_info['available_cameras']:
                if cam['working']:
                    st.write(f"📷 **Camera {cam['index']}**: {cam['resolution']} ✅")
        else:
            st.error("❌ Camera not available")
            st.info("💡 **Troubleshooting**: Try the camera test tool or check hardware connection.")

            # Provide camera test option
            if st.button("🔧 Run Camera Test Tool"):
                st.code("""
# Run this in terminal to test camera:
cd "/home/<USER>/Documents/Sign Language Detector"
python3 camera_test_tool.py
                """)
            return

    with col2:
        st.markdown("### 🧠 Model Selection")
        available_models = get_available_models()

        if not available_models:
            st.warning("⚠️ No trained models available.")
            st.info("Train a model first using the Model Training section.")
            return

        model_names = [model['name'] for model in available_models]
        selected_model = st.selectbox("Select Model", model_names)

        # Get selected model info
        model_info = next(m for m in available_models if m['name'] == selected_model)

        st.markdown("### 📊 Model Information")
        st.markdown(f"""
        - **Size**: {model_info['size_mb']:.1f} MB
        - **Created**: {model_info['created'][:19]}
        """)

    # Enhanced testing interface with visual overlay features
    st.markdown("### 🚀 Enhanced Real-time Testing")

    # Highlight new visual features
    st.info("""
    **🎯 NEW ENHANCED VISUAL FEATURES:**
    • **Large Letter Display**: Prominent predicted letters on camera feed
    • **Prediction Stability**: Visual indicators for stable predictions
    • **Confidence Scores**: Real-time confidence percentages
    • **Prediction History**: Recent predictions tracking
    • **Gesture Guidance**: On-screen instructions when no hand detected
    • **Performance Stats**: FPS, frame count, prediction rate
    • **Screenshot Function**: Save predictions with 'S' key
    """)

    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        confidence_threshold = st.slider(
            "Confidence Threshold",
            min_value=0.1,
            max_value=1.0,
            value=0.8,
            step=0.05,
            help="Minimum confidence for predictions (affects visual indicators)"
        )

    with col2:
        testing_mode = st.selectbox(
            "Visual Display Mode",
            ["Enhanced Overlays", "Standard Window", "Headless Test"],
            help="Enhanced Overlays: Full visual feedback system"
        )

    with col3:
        test_duration = st.number_input(
            "Test Duration (seconds)",
            min_value=10,
            max_value=300,
            value=60,
            help="Duration for headless testing"
        )

    # Testing buttons
    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        if st.button("🎥 Start Real-time Testing", type="primary"):
            st.session_state.testing = True
            st.session_state.testing_mode = testing_mode
            st.session_state.selected_model = selected_model
            st.session_state.confidence_threshold = confidence_threshold

    with col2:
        if st.button("🔧 Test Camera Only"):
            st.session_state.camera_test = True

    with col3:
        if st.button("📊 Quick Headless Test"):
            st.session_state.headless_test = True
            st.session_state.test_duration = test_duration

    # Instructions and tips
    st.markdown("### 📋 Testing Instructions")

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown("""
        **🎯 Enhanced Real-time Testing:**
        1. Select a trained model
        2. Choose "Enhanced Overlays" mode
        3. Click 'Start Real-time Testing'
        4. OpenCV window opens with visual overlays
        5. Show ASL gestures to camera
        6. Watch real-time predictions appear

        **📊 Visual Feedback Elements:**
        - **Large letter display** (top-right)
        - **Confidence percentage** below letter
        - **Stability indicator** (green = stable)
        - **Recent predictions** history panel
        - **Performance statistics** (bottom-left)
        """)

    with col2:
        st.markdown("""
        **💡 Enhanced Usage Tips:**
        - **Position hand in center** of camera view
        - **Hold gesture steady** for 2-3 seconds
        - **Watch stability indicator** turn green
        - **Good lighting** improves accuracy
        - **Clear background** reduces noise

        **🎮 Enhanced Controls:**
        - **'Q'**: Quit application
        - **'R'**: Reset statistics
        - **'S'**: Save screenshot with prediction
        - **'H'**: Show help in terminal
        """)

    # Display camera information
    st.markdown("### 📷 Camera Configuration")
    st.info(f"""
    **Active Camera**: Index {camera_info['working_camera_index']} |
    **Resolution**: 640x480 |
    **Expected FPS**: 30 |
    **Display**: Wayland/X11 Compatible
    """)

    # Enhanced testing execution
    if st.session_state.get('testing', False):
        st.markdown("### 🎥 Real-time Testing Active")

        testing_mode = st.session_state.get('testing_mode', 'Standard Window')
        selected_model = st.session_state.get('selected_model', model_names[0])
        confidence_threshold = st.session_state.get('confidence_threshold', 0.8)

        # Get model info
        model_info = next(m for m in available_models if m['name'] == selected_model)

        st.info(f"""
        **🚀 Starting ASL Recognition**
        - **Model**: {selected_model}
        - **Mode**: {testing_mode}
        - **Camera**: Index {camera_info['working_camera_index']}
        - **Confidence**: {confidence_threshold}

        **OpenCV window will appear shortly...**
        """)

        # Show terminal command for reference
        with st.expander("🔧 Terminal Command (for manual execution)"):
            st.code(f"""
# Run this command in terminal for direct access:
cd "/home/<USER>/Documents/Sign Language Detector"
python3 -c "
from src.inference import ASLInferenceEngine
engine = ASLInferenceEngine('{model_info['path']}')
engine.run_inference(camera_index={camera_info['working_camera_index']})
"
            """)

        try:
            from src.inference import ASLInferenceEngine

            model_path = model_info['path']
            engine = ASLInferenceEngine(model_path)

            if testing_mode == "Enhanced Overlays":
                st.write("🎨 **Enhanced Overlays Mode**: Full visual feedback with overlays")
                engine.run_inference(camera_index=camera_info['working_camera_index'],
                                   window_name="Enhanced ASL Recognition")

            elif testing_mode == "Standard Window":
                st.write("🎯 **Standard Window Mode**: Basic OpenCV window with predictions")
                engine.run_inference(camera_index=camera_info['working_camera_index'])

            elif testing_mode == "Headless Test":
                duration = st.session_state.get('test_duration', 60)
                st.write(f"🤖 **Headless Mode**: {duration} seconds without display")

                # Create progress bar
                progress_bar = st.progress(0)
                status_text = st.empty()

                import time
                start_time = time.time()

                # Run headless inference
                engine.run_inference_headless(
                    camera_index=camera_info['working_camera_index'],
                    duration=duration
                )

                progress_bar.progress(1.0)
                status_text.success("✅ Headless testing completed!")

            st.success("✅ Testing session completed successfully!")

        except Exception as e:
            st.error(f"❌ Testing failed: {str(e)}")
            st.info("💡 Try using the camera test tool or check the terminal command above.")

        finally:
            st.session_state.testing = False

    # Camera test execution
    if st.session_state.get('camera_test', False):
        st.markdown("### 📹 Camera Test Active")
        st.info("Testing camera functionality without model inference...")

        try:
            st.code(f"""
# Camera test command:
cd "/home/<USER>/Documents/Sign Language Detector"
python3 camera_test_tool.py
            """)

            st.success("✅ Use the terminal command above to run the camera test tool.")

        finally:
            st.session_state.camera_test = False

    # Headless test execution
    if st.session_state.get('headless_test', False):
        st.markdown("### 🤖 Quick Headless Test Active")

        duration = st.session_state.get('test_duration', 60)

        try:
            from src.inference import ASLInferenceEngine

            model_path = model_info['path']
            engine = ASLInferenceEngine(model_path)

            st.info(f"Running {duration}-second headless test...")

            # Create progress simulation
            progress_bar = st.progress(0)
            status_text = st.empty()

            import time
            for i in range(duration):
                progress_bar.progress((i + 1) / duration)
                status_text.text(f"Processing... {i + 1}/{duration} seconds")
                time.sleep(1)

            st.success("✅ Headless test completed! Check terminal for detailed output.")

        except Exception as e:
            st.error(f"❌ Headless test failed: {str(e)}")

        finally:
            st.session_state.headless_test = False


def show_system_info_page():
    """Display system information and diagnostics with enhanced camera diagnostics"""

    st.markdown("## ⚙️ System Information & Diagnostics")

    # Enhanced camera diagnostics section
    st.markdown("### 📹 Camera Diagnostics")

    camera_info = get_camera_info()

    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        st.markdown("**Camera Status**")
        if camera_info['camera_accessible']:
            st.success(f"✅ Working Camera Found")
            st.info(f"**Index**: {camera_info['working_camera_index']}")
        else:
            st.error("❌ No Working Camera")

    with col2:
        st.markdown("**Available Cameras**")
        for cam in camera_info['available_cameras']:
            status = "✅" if cam['working'] else "❌"
            st.write(f"{status} Camera {cam['index']}: {cam['resolution']}")

    with col3:
        st.markdown("**Quick Actions**")
        if st.button("🔧 Run Camera Test"):
            st.code("""
# Terminal command:
cd "/home/<USER>/Documents/Sign Language Detector"
python3 camera_test_tool.py
            """)

        if st.button("🎥 Test OpenCV Window"):
            st.code("""
# Test OpenCV window display:
python3 -c "
import cv2
import numpy as np
img = np.zeros((480,640,3), dtype=np.uint8)
cv2.putText(img, 'Test Window', (200,240), cv2.FONT_HERSHEY_SIMPLEX, 1, (0,255,0), 2)
cv2.imshow('Test', img)
cv2.waitKey(5000)
cv2.destroyAllWindows()
"
            """)

    # Display environment information
    st.markdown("### 🖥️ Display Environment")

    import os
    display_info = {
        "DISPLAY": os.environ.get('DISPLAY', 'Not set'),
        "XDG_SESSION_TYPE": os.environ.get('XDG_SESSION_TYPE', 'Not set'),
        "WAYLAND_DISPLAY": os.environ.get('WAYLAND_DISPLAY', 'Not set'),
        "QT_QPA_PLATFORM": os.environ.get('QT_QPA_PLATFORM', 'Not set')
    }

    for key, value in display_info.items():
        st.write(f"**{key}**: {value}")

    # System status
    st.markdown("### 💻 Hardware Status")
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Camera Hardware**")

        camera_status = validate_camera_access()
        st.markdown(f"**Camera Access**: {'✅ Available' if camera_status else '❌ Not Available'}")

        # Show working camera details
        if camera_info['camera_accessible']:
            st.markdown(f"**Working Camera**: Index {camera_info['working_camera_index']}")
            st.markdown("**Expected Resolution**: 640x480")
            st.markdown("**Expected FPS**: 30")

        # GPU information
        import tensorflow as tf
        gpu_devices = tf.config.list_physical_devices('GPU')
        st.markdown(f"**GPU**: {'✅ Available' if gpu_devices else '❌ Not Available (CPU only)'}")

        if gpu_devices:
            for i, gpu in enumerate(gpu_devices):
                st.markdown(f"  - GPU {i}: {gpu.name}")
    
    with col2:
        st.markdown("### Software Versions")
        
        import cv2
        import mediapipe as mp
        
        st.markdown(f"**TensorFlow**: {tf.__version__}")
        st.markdown(f"**OpenCV**: {cv2.__version__}")
        st.markdown(f"**MediaPipe**: {mp.__version__}")
        st.markdown(f"**Streamlit**: {st.__version__}")
    
    # Project statistics
    st.markdown("### Project Statistics")
    
    data_summary = get_collection_summary()
    available_models = get_available_models()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Samples", sum(data_summary.values()))
    with col2:
        st.metric("Letters with Data", len([c for c in data_summary.values() if c > 0]))
    with col3:
        st.metric("Available Models", len(available_models))
    with col4:
        total_size = sum(model['size_mb'] for model in available_models)
        st.metric("Total Model Size", f"{total_size:.1f} MB")
    
    # Detailed system info
    if st.button("Show Detailed System Info"):
        print_system_info()
        st.success("✅ Detailed system information printed to console.")


if __name__ == "__main__":
    main()
