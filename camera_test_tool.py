#!/usr/bin/env python3
"""
Camera Test Tool for ASL Recognition System
Comprehensive camera testing and window display troubleshooting
"""

import cv2
import numpy as np
import time
import os
import sys

def test_display_environment():
    """Test the display environment configuration"""
    print("🖥️  Display Environment Check:")
    print(f"   DISPLAY: {os.environ.get('DISPLAY', 'Not set')}")
    print(f"   XDG_SESSION_TYPE: {os.environ.get('XDG_SESSION_TYPE', 'Not set')}")
    print(f"   WAYLAND_DISPLAY: {os.environ.get('WAYLAND_DISPLAY', 'Not set')}")
    print(f"   QT_QPA_PLATFORM: {os.environ.get('QT_QPA_PLATFORM', 'Not set')}")
    print()

def test_opencv_window():
    """Test basic OpenCV window functionality"""
    print("🔧 Testing Basic OpenCV Window...")
    
    try:
        # Create test image
        test_img = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.putText(test_img, 'OpenCV Window Test', (150, 240), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(test_img, 'Press any key to continue', (120, 300), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Display window
        cv2.namedWindow('OpenCV Test', cv2.WINDOW_NORMAL)
        cv2.imshow('OpenCV Test', test_img)
        
        print("✅ OpenCV window created successfully")
        print("   Window should be visible. Press any key to continue...")
        
        key = cv2.waitKey(5000)  # 5 second timeout
        cv2.destroyAllWindows()
        
        if key != -1:
            print(f"✅ Key detected: {chr(key & 0xFF)}")
            return True
        else:
            print("⚠️  No key pressed (timeout)")
            return True  # Window still worked
            
    except Exception as e:
        print(f"❌ OpenCV window test failed: {e}")
        return False

def find_working_cameras():
    """Find all working camera indices using camera manager"""
    print("📹 Scanning for Working Cameras with Camera Manager...")

    # Import camera manager
    sys.path.append('/home/<USER>/Documents/Sign Language Detector')
    from src.camera_manager import get_camera_manager

    camera_manager = get_camera_manager()
    cameras = camera_manager.scan_cameras(force_rescan=True)

    working_cameras = []
    for camera in cameras:
        if camera.working:
            print(f"✅ Camera {camera.index}: Working ({camera.resolution}, {camera.fps:.1f} FPS)")
            working_cameras.append(camera.index)
        else:
            print(f"❌ Camera {camera.index}: Not working")

    # Show active camera
    active_camera = camera_manager.get_active_camera()
    if active_camera is not None:
        print(f"🎯 Active Camera: {active_camera} (Persistent)")
    else:
        print("⚠️  No active camera set")

    return working_cameras

def test_camera_window(camera_index):
    """Test camera with window display"""
    print(f"🎥 Testing Camera {camera_index} with Window Display...")
    
    try:
        cap = cv2.VideoCapture(camera_index)
        
        if not cap.isOpened():
            print(f"❌ Cannot open camera {camera_index}")
            return False
        
        # Set camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        # Create window
        window_name = f"Camera {camera_index} Test"
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 640, 480)
        
        print(f"✅ Camera {camera_index} opened successfully")
        print("   Live camera window should appear. Press 'q' to quit, 's' to screenshot...")
        
        frame_count = 0
        start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ Failed to read frame")
                break
            
            frame_count += 1
            
            # Add overlay information
            cv2.putText(frame, f"Camera {camera_index} - Frame {frame_count}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, "Press 'q' to quit, 's' for screenshot", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Calculate FPS
            elapsed = time.time() - start_time
            if elapsed > 0:
                fps = frame_count / elapsed
                cv2.putText(frame, f"FPS: {fps:.1f}", 
                           (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
            # Display frame
            cv2.imshow(window_name, frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("✅ Quit key pressed - camera window working!")
                break
            elif key == ord('s'):
                screenshot_name = f"camera_{camera_index}_screenshot.jpg"
                cv2.imwrite(screenshot_name, frame)
                print(f"📸 Screenshot saved: {screenshot_name}")
            
            # Auto-quit after 30 seconds for testing
            if elapsed > 30:
                print("⏰ Auto-quit after 30 seconds")
                break
        
        cap.release()
        cv2.destroyWindow(window_name)
        
        print(f"✅ Camera {camera_index} window test completed successfully!")
        print(f"   Processed {frame_count} frames in {elapsed:.1f} seconds")
        return True
        
    except Exception as e:
        print(f"❌ Camera {camera_index} window test failed: {e}")
        return False

def test_asl_inference():
    """Test ASL inference with working camera"""
    print("🧠 Testing ASL Inference with Camera...")
    
    try:
        # Import ASL inference
        sys.path.append('/home/<USER>/Documents/Sign Language Detector')
        from src.inference import ASLInferenceEngine
        
        # Load model
        model_path = '/home/<USER>/Documents/Sign Language Detector/data/models/external_dataset_demo.h5'
        if not os.path.exists(model_path):
            print(f"❌ Model not found: {model_path}")
            return False
        
        engine = ASLInferenceEngine(model_path)
        print("✅ ASL model loaded successfully")
        
        # Test with working camera
        working_cameras = find_working_cameras()
        if not working_cameras:
            print("❌ No working cameras found for ASL inference")
            return False
        
        camera_index = working_cameras[0]
        print(f"🚀 Starting ASL inference with camera {camera_index}...")
        print("   ASL recognition window should appear with live predictions!")
        
        # Run inference for limited time
        engine.run_inference(camera_index=camera_index)
        
        return True
        
    except Exception as e:
        print(f"❌ ASL inference test failed: {e}")
        return False

def main():
    """Main camera testing function"""
    print("🎯 ASL Recognition System - Camera Window Test Tool")
    print("=" * 60)
    
    # Test 1: Display environment
    test_display_environment()
    
    # Test 2: Basic OpenCV window
    if not test_opencv_window():
        print("❌ Basic OpenCV windows not working. Display environment issue.")
        return
    
    # Test 3: Find working cameras
    working_cameras = find_working_cameras()
    if not working_cameras:
        print("❌ No working cameras found. Hardware issue.")
        return
    
    print(f"\n✅ Found {len(working_cameras)} working camera(s): {working_cameras}")
    
    # Test 4: Test camera window for each working camera
    for camera_index in working_cameras:
        if test_camera_window(camera_index):
            print(f"✅ Camera {camera_index} window display working!")
            
            # Test 5: ASL inference
            response = input(f"\nTest ASL inference with camera {camera_index}? (y/n): ")
            if response.lower() == 'y':
                test_asl_inference()
            break
    
    print("\n🎉 Camera testing completed!")
    print("If camera windows appeared, your display system is working correctly.")

if __name__ == "__main__":
    main()
