#!/usr/bin/env python3
"""
Camera Test Tool for ASL Recognition System
Comprehensive camera testing and window display troubleshooting
"""

import cv2
import numpy as np
import time
import os
import sys
import json
from typing import Dict, Optional, <PERSON><PERSON>

def run_comprehensive_display_diagnostics() -> Dict:
    """Run comprehensive display diagnostics"""
    try:
        # Import display diagnostics
        sys.path.append('/home/<USER>/Documents/Sign Language Detector')
        from src.display_diagnostics import run_display_diagnostics

        print("🔍 Running Comprehensive Display Diagnostics...")
        print("=" * 60)

        results = run_display_diagnostics()

        # Display results
        print("\n📊 DISPLAY ENVIRONMENT:")
        env = results['environment']
        print(f"   Display Server: {env['display_server']}")
        print(f"   DISPLAY: {env['display_variable'] or 'Not set'}")
        print(f"   Session Type: {env['session_type'] or 'Unknown'}")
        print(f"   OpenCV GUI Support: {'✅ Yes' if env['opencv_gui_support'] else '❌ No'}")
        print(f"   OpenCV Version: {env['opencv_version']}")

        print("\n🧪 WINDOW TESTS:")
        for test in results['window_tests']:
            status = "✅ PASS" if test['success'] else "❌ FAIL"
            print(f"   {test['test_name']}: {status}")
            if not test['success'] and test['error_message']:
                print(f"      Error: {test['error_message']}")

        print("\n📹 CAMERA TESTS:")
        for test in results['camera_tests']:
            status = "✅ PASS" if test['success'] else "❌ FAIL"
            print(f"   {test['test_name']}: {status}")
            if not test['success'] and test['error_message']:
                print(f"      Error: {test['error_message']}")

        print(f"\n🎯 OVERALL STATUS: {results['overall_status'].upper()}")

        print("\n💡 RECOMMENDATIONS:")
        for rec in results['recommendations']:
            print(f"   {rec}")

        return results

    except Exception as e:
        print(f"❌ Error running diagnostics: {e}")
        return {}

def test_display_environment():
    """Test the display environment configuration (basic)"""
    print("🖥️  Basic Display Environment Check:")
    print(f"   DISPLAY: {os.environ.get('DISPLAY', 'Not set')}")
    print(f"   XDG_SESSION_TYPE: {os.environ.get('XDG_SESSION_TYPE', 'Not set')}")
    print(f"   WAYLAND_DISPLAY: {os.environ.get('WAYLAND_DISPLAY', 'Not set')}")
    print(f"   QT_QPA_PLATFORM: {os.environ.get('QT_QPA_PLATFORM', 'Not set')}")
    print()

def test_opencv_window_with_fallback() -> Tuple[bool, str]:
    """Test OpenCV window functionality with comprehensive error handling"""
    print("🔧 Testing OpenCV Window Display...")

    try:
        # Test 1: Basic window creation
        print("   Step 1: Creating window...")
        cv2.namedWindow('OpenCV_Test', cv2.WINDOW_NORMAL)
        print("   ✅ Window created")

        # Test 2: Image display
        print("   Step 2: Displaying image...")
        test_img = np.zeros((480, 640, 3), dtype=np.uint8)
        test_img[:] = (50, 50, 50)  # Dark gray background

        # Add informative text
        cv2.putText(test_img, 'OpenCV Window Test', (150, 200),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(test_img, 'Auto-closing in 3 seconds...', (120, 250),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(test_img, 'Press any key to close early', (100, 300),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

        cv2.imshow('OpenCV_Test', test_img)
        print("   ✅ Image displayed")

        # Test 3: Window interaction
        print("   Step 3: Testing window interaction...")
        print("   🔍 Window should be visible now...")

        key = cv2.waitKey(3000)  # 3 second timeout
        cv2.destroyAllWindows()

        if key != -1:
            print(f"   ✅ User interaction detected (key: {key & 0xFF})")
            return True, "Window display successful with user interaction"
        else:
            print("   ⚠️  No user interaction (auto-timeout)")
            return True, "Window display successful (auto-timeout)"

    except Exception as e:
        error_msg = str(e)
        print(f"   ❌ OpenCV window test failed: {error_msg}")

        # Provide specific guidance based on error
        if "cannot connect to X server" in error_msg.lower():
            guidance = "X11 display server not available - use web interface"
        elif "qt" in error_msg.lower():
            guidance = "Qt platform issue - try setting QT_QPA_PLATFORM=xcb"
        elif "wayland" in error_msg.lower():
            guidance = "Wayland compatibility issue - use X11 session or web interface"
        else:
            guidance = "OpenCV GUI not available - use Streamlit web interface"

        print(f"   💡 Guidance: {guidance}")
        return False, f"Window display failed: {error_msg}"

def test_opencv_window():
    """Test basic OpenCV window functionality (legacy)"""
    success, message = test_opencv_window_with_fallback()
    return success

def find_working_cameras():
    """Find all working camera indices using camera manager"""
    print("📹 Scanning for Working Cameras with Camera Manager...")

    # Import camera manager
    sys.path.append('/home/<USER>/Documents/Sign Language Detector')
    from src.camera_manager import get_camera_manager

    camera_manager = get_camera_manager()
    cameras = camera_manager.scan_cameras(force_rescan=True)

    working_cameras = []
    for camera in cameras:
        if camera.working:
            print(f"✅ Camera {camera.index}: Working ({camera.resolution}, {camera.fps:.1f} FPS)")
            working_cameras.append(camera.index)
        else:
            print(f"❌ Camera {camera.index}: Not working")

    # Show active camera
    active_camera = camera_manager.get_active_camera()
    if active_camera is not None:
        print(f"🎯 Active Camera: {active_camera} (Persistent)")
    else:
        print("⚠️  No active camera set")

    return working_cameras

def test_camera_window(camera_index):
    """Test camera with window display"""
    print(f"🎥 Testing Camera {camera_index} with Window Display...")
    
    try:
        cap = cv2.VideoCapture(camera_index)
        
        if not cap.isOpened():
            print(f"❌ Cannot open camera {camera_index}")
            return False
        
        # Set camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        # Create window
        window_name = f"Camera {camera_index} Test"
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 640, 480)
        
        print(f"✅ Camera {camera_index} opened successfully")
        print("   Live camera window should appear. Press 'q' to quit, 's' to screenshot...")
        
        frame_count = 0
        start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ Failed to read frame")
                break
            
            frame_count += 1
            
            # Add overlay information
            cv2.putText(frame, f"Camera {camera_index} - Frame {frame_count}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, "Press 'q' to quit, 's' for screenshot", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Calculate FPS
            elapsed = time.time() - start_time
            if elapsed > 0:
                fps = frame_count / elapsed
                cv2.putText(frame, f"FPS: {fps:.1f}", 
                           (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
            # Display frame
            cv2.imshow(window_name, frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("✅ Quit key pressed - camera window working!")
                break
            elif key == ord('s'):
                screenshot_name = f"camera_{camera_index}_screenshot.jpg"
                cv2.imwrite(screenshot_name, frame)
                print(f"📸 Screenshot saved: {screenshot_name}")
            
            # Auto-quit after 30 seconds for testing
            if elapsed > 30:
                print("⏰ Auto-quit after 30 seconds")
                break
        
        cap.release()
        cv2.destroyWindow(window_name)
        
        print(f"✅ Camera {camera_index} window test completed successfully!")
        print(f"   Processed {frame_count} frames in {elapsed:.1f} seconds")
        return True
        
    except Exception as e:
        print(f"❌ Camera {camera_index} window test failed: {e}")
        return False

def test_asl_inference():
    """Test ASL inference with working camera"""
    print("🧠 Testing ASL Inference with Camera...")
    
    try:
        # Import ASL inference
        sys.path.append('/home/<USER>/Documents/Sign Language Detector')
        from src.inference import ASLInferenceEngine
        
        # Load model
        model_path = '/home/<USER>/Documents/Sign Language Detector/data/models/external_dataset_demo.h5'
        if not os.path.exists(model_path):
            print(f"❌ Model not found: {model_path}")
            return False
        
        engine = ASLInferenceEngine(model_path)
        print("✅ ASL model loaded successfully")
        
        # Test with working camera
        working_cameras = find_working_cameras()
        if not working_cameras:
            print("❌ No working cameras found for ASL inference")
            return False
        
        camera_index = working_cameras[0]
        print(f"🚀 Starting ASL inference with camera {camera_index}...")
        print("   ASL recognition window should appear with live predictions!")
        
        # Run inference for limited time
        engine.run_inference(camera_index=camera_index)
        
        return True
        
    except Exception as e:
        print(f"❌ ASL inference test failed: {e}")
        return False

def main():
    """Main camera testing function"""
    print("🎯 ASL Recognition System - Camera Window Test Tool")
    print("=" * 60)
    
    # Test 1: Display environment
    test_display_environment()
    
    # Test 2: Basic OpenCV window
    if not test_opencv_window():
        print("❌ Basic OpenCV windows not working. Display environment issue.")
        return
    
    # Test 3: Find working cameras
    working_cameras = find_working_cameras()
    if not working_cameras:
        print("❌ No working cameras found. Hardware issue.")
        return
    
    print(f"\n✅ Found {len(working_cameras)} working camera(s): {working_cameras}")
    
    # Test 4: Test camera window for each working camera
    for camera_index in working_cameras:
        if test_camera_window(camera_index):
            print(f"✅ Camera {camera_index} window display working!")
            
            # Test 5: ASL inference
            response = input(f"\nTest ASL inference with camera {camera_index}? (y/n): ")
            if response.lower() == 'y':
                test_asl_inference()
            break
    
    print("\n🎉 Camera testing completed!")
    print("If camera windows appeared, your display system is working correctly.")

def test_camera_display_comprehensive(camera_index: int) -> Tuple[bool, Dict]:
    """Comprehensive camera display test with fallback options"""
    print(f"📹 Testing Camera {camera_index} Display (Comprehensive)...")

    results = {
        'camera_accessible': False,
        'opencv_display': False,
        'frames_captured': 0,
        'display_method': None,
        'error_message': None,
        'recommendations': []
    }

    try:
        # Test 1: Camera accessibility
        print("   Step 1: Testing camera accessibility...")
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            results['error_message'] = f"Cannot open camera {camera_index}"
            print(f"   ❌ {results['error_message']}")
            return False, results

        # Test basic frame capture
        ret, test_frame = cap.read()
        if not ret or test_frame is None:
            results['error_message'] = "Camera opens but no frames available"
            print(f"   ❌ {results['error_message']}")
            cap.release()
            return False, results

        results['camera_accessible'] = True
        print(f"   ✅ Camera accessible ({test_frame.shape})")

        # Test 2: OpenCV window display
        print("   Step 2: Testing OpenCV window display...")
        try:
            cv2.namedWindow(f'Camera_{camera_index}_Test', cv2.WINDOW_NORMAL)

            frame_count = 0
            start_time = time.time()
            display_success = False

            print("   🔍 Camera window should be visible...")
            print("   ⏱️  Running for 5 seconds (press 'q' to quit early)")

            while time.time() - start_time < 5.0:  # 5 second test
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # Add informative overlay
                cv2.putText(frame, f'Camera {camera_index} - Frame {frame_count}',
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                cv2.putText(frame, f'Time: {time.time() - start_time:.1f}s',
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                cv2.putText(frame, "Press 'q' to quit", (10, 90),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                cv2.imshow(f'Camera_{camera_index}_Test', frame)

                key = cv2.waitKey(30) & 0xFF
                if key == ord('q'):
                    print("   ✅ User quit camera display test")
                    break

                display_success = True

            cv2.destroyAllWindows()

            results['opencv_display'] = display_success
            results['frames_captured'] = frame_count
            results['display_method'] = 'opencv_window'

            if display_success:
                print(f"   ✅ OpenCV display successful ({frame_count} frames)")
            else:
                print("   ❌ OpenCV display failed")
                results['error_message'] = "OpenCV window display failed"

        except Exception as e:
            results['error_message'] = f"OpenCV display error: {str(e)}"
            print(f"   ❌ OpenCV display error: {e}")

            # Provide specific recommendations
            if "cannot connect to X server" in str(e).lower():
                results['recommendations'].append("Use Streamlit web interface (X11 not available)")
            elif "qt" in str(e).lower():
                results['recommendations'].append("Try: export QT_QPA_PLATFORM=xcb")
            elif "wayland" in str(e).lower():
                results['recommendations'].append("Switch to X11 session or use web interface")
            else:
                results['recommendations'].append("Use Streamlit web interface for camera access")

        cap.release()

        # Test 3: Alternative methods if OpenCV fails
        if not results['opencv_display']:
            print("   Step 3: Testing alternative access methods...")

            # Test headless capture (for web interface)
            try:
                cap = cv2.VideoCapture(camera_index)
                ret, frame = cap.read()
                if ret:
                    print("   ✅ Headless camera capture works - web interface available")
                    results['display_method'] = 'web_interface'
                    results['recommendations'].append("✅ Use Streamlit web interface for reliable camera access")
                cap.release()
            except Exception as e:
                print(f"   ❌ Headless capture also failed: {e}")

        return results['camera_accessible'], results

    except Exception as e:
        results['error_message'] = f"Comprehensive test failed: {str(e)}"
        print(f"   ❌ Comprehensive camera test failed: {e}")
        return False, results

def run_enhanced_camera_test():
    """Run enhanced camera test with display diagnostics"""
    print("🚀 Enhanced Camera Test with Display Diagnostics")
    print("=" * 60)

    # Step 1: Run display diagnostics
    print("\n📊 STEP 1: Display Environment Diagnostics")
    diag_results = run_comprehensive_display_diagnostics()

    # Step 2: Test cameras with comprehensive display testing
    print("\n📹 STEP 2: Camera Display Testing")

    # Import camera manager
    sys.path.append('/home/<USER>/Documents/Sign Language Detector')
    from src.camera_manager import get_camera_manager

    camera_manager = get_camera_manager()
    camera_info = camera_manager.get_camera_info()

    if not camera_info['camera_accessible']:
        print("❌ No cameras available for testing")
        return

    active_camera = camera_info['active_camera_index']
    print(f"🎯 Testing active camera: {active_camera}")

    # Run comprehensive camera display test
    success, results = test_camera_display_comprehensive(active_camera)

    # Step 3: Provide recommendations
    print("\n💡 STEP 3: Recommendations")

    if results['opencv_display']:
        print("✅ OpenCV window display works - you can use:")
        print("   • Direct inference: python3 -c \"from src.inference import ASLInferenceEngine; ASLInferenceEngine('model.h5').run_inference()\"")
        print("   • Enhanced demo: python3 demo_enhanced_features.py")
    else:
        print("⚠️  OpenCV window display not available - recommended alternatives:")
        print("   • Streamlit web interface: http://localhost:8507")
        print("   • Web-based camera access with full functionality")

        for rec in results['recommendations']:
            print(f"   • {rec}")

    print(f"\n🎯 Overall Status: {'✅ WORKING' if success else '❌ ISSUES DETECTED'}")

if __name__ == "__main__":
    # Check if enhanced mode requested
    if len(sys.argv) > 1 and sys.argv[1] == "--enhanced":
        run_enhanced_camera_test()
    else:
        main()
