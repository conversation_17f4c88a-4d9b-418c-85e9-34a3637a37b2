#!/usr/bin/env python3
"""
Enhanced Camera Test Tool with Stable OpenCV Window Management
Fixes camera window stability issues and provides comprehensive testing
"""

import sys
import os
import time
import cv2
import numpy as np
import argparse
from pathlib import Path

# Add project path
sys.path.append('/home/<USER>/Documents/Sign Language Detector')

from src.camera_window_manager import get_window_manager
from src.camera_manager import get_camera_manager

def print_header(title: str):
    """Print formatted header"""
    print("\n" + "=" * 60)
    print(f"🔧 {title}")
    print("=" * 60)

def print_status(message: str, success: bool):
    """Print status message"""
    status = "✅" if success else "❌"
    print(f"   {status} {message}")

def test_camera_window_stability():
    """Test camera window stability with enhanced management"""
    print_header("CAMERA WINDOW STABILITY TEST")
    
    try:
        # Get camera manager
        camera_manager = get_camera_manager()
        camera_info = camera_manager.get_camera_info()
        
        if not camera_info['camera_accessible']:
            print_status("No camera available - testing with synthetic frames", True)
            test_synthetic_camera_window()
            return True
        
        # Test with real camera
        camera_index = camera_info['active_camera_index']
        print(f"Testing with camera {camera_index}")
        
        # Initialize camera
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            print_status("Failed to open camera", False)
            return False
        
        # Set camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        print_status("Camera opened successfully", True)
        
        # Test window management
        with get_window_manager() as wm:
            window_name = "Camera Stability Test"
            
            # Create window
            success = wm.create_window(window_name, 640, 480, resizable=True)
            print_status(f"Window '{window_name}' created", success)
            
            if not success:
                cap.release()
                return False
            
            print("\n🎯 Camera Window Test (10 seconds)")
            print("   Press 'q' to quit early")
            print("   Press 'r' to restart test")
            print("   Press 's' to take screenshot")
            
            start_time = time.time()
            frame_count = 0
            screenshot_count = 0
            
            while time.time() - start_time < 10.0:
                ret, frame = cap.read()
                if not ret:
                    print_status("Failed to read camera frame", False)
                    break
                
                # Flip frame for mirror effect
                frame = cv2.flip(frame, 1)
                
                # Add overlay information
                overlay_frame = add_test_overlay(frame, frame_count, time.time() - start_time)
                
                # Display frame
                success = wm.show_frame(window_name, overlay_frame)
                if not success:
                    print_status("Failed to display frame", False)
                    break
                
                # Handle key presses
                key = wm.wait_key(30)
                
                if key == ord('q'):
                    print("\n   User requested quit")
                    break
                elif key == ord('r'):
                    print("\n   Restarting test...")
                    start_time = time.time()
                    frame_count = 0
                elif key == ord('s'):
                    screenshot_count += 1
                    screenshot_name = f"camera_test_screenshot_{screenshot_count}.jpg"
                    cv2.imwrite(screenshot_name, overlay_frame)
                    print(f"\n   Screenshot saved: {screenshot_name}")
                
                frame_count += 1
                
                # Check window responsiveness every 30 frames
                if frame_count % 30 == 0:
                    if not wm.is_window_open(window_name):
                        print_status("Window became unresponsive", False)
                        break
            
            # Test results
            total_time = time.time() - start_time
            fps = frame_count / total_time if total_time > 0 else 0
            
            print(f"\n📊 Test Results:")
            print(f"   Duration: {total_time:.2f} seconds")
            print(f"   Frames processed: {frame_count}")
            print(f"   Average FPS: {fps:.2f}")
            print(f"   Window responsive: {'✅ Yes' if wm.is_window_open(window_name) else '❌ No'}")
            
            # Test window reopening
            print("\n🔄 Testing Window Reopening...")
            wm.close_window(window_name)
            time.sleep(0.5)
            
            success = wm.create_window(window_name + " - Reopened", 640, 480)
            print_status("Window reopened successfully", success)
            
            if success:
                # Show a few more frames
                for i in range(30):
                    ret, frame = cap.read()
                    if ret:
                        frame = cv2.flip(frame, 1)
                        overlay_frame = add_test_overlay(frame, i, 1.0)
                        wm.show_frame(window_name + " - Reopened", overlay_frame)
                        wm.wait_key(33)
                
                print_status("Window reopening test completed", True)
        
        cap.release()
        return True
        
    except Exception as e:
        print_status(f"Camera window test failed: {e}", False)
        return False

def test_synthetic_camera_window():
    """Test window stability with synthetic camera frames"""
    print("🎨 Testing with synthetic camera frames...")
    
    with get_window_manager() as wm:
        window_name = "Synthetic Camera Test"
        
        # Create window
        success = wm.create_window(window_name, 640, 480)
        print_status(f"Synthetic window created", success)
        
        if not success:
            return False
        
        print("\n🎯 Synthetic Camera Test (5 seconds)")
        print("   Press 'q' to quit early")
        
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 5.0:
            # Create synthetic frame
            frame = create_synthetic_frame(640, 480, frame_count)
            
            # Add test overlay
            overlay_frame = add_test_overlay(frame, frame_count, time.time() - start_time)
            
            # Display frame
            success = wm.show_frame(window_name, overlay_frame)
            if not success:
                print_status("Failed to display synthetic frame", False)
                break
            
            # Handle key presses
            key = wm.wait_key(33)  # ~30 FPS
            if key == ord('q'):
                break
            
            frame_count += 1
        
        total_time = time.time() - start_time
        fps = frame_count / total_time if total_time > 0 else 0
        
        print(f"\n📊 Synthetic Test Results:")
        print(f"   Duration: {total_time:.2f} seconds")
        print(f"   Frames generated: {frame_count}")
        print(f"   Average FPS: {fps:.2f}")
        
        return True

def create_synthetic_frame(width: int, height: int, frame_number: int) -> np.ndarray:
    """Create a synthetic camera frame for testing"""
    # Create gradient background
    frame = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Animated gradient
    offset = (frame_number * 2) % 255
    for y in range(height):
        color_value = (y + offset) % 255
        frame[y, :] = [color_value // 3, color_value // 2, color_value]
    
    # Add moving circle
    center_x = int(width // 2 + 100 * np.sin(frame_number * 0.1))
    center_y = int(height // 2 + 50 * np.cos(frame_number * 0.1))
    cv2.circle(frame, (center_x, center_y), 30, (255, 255, 255), -1)
    
    # Add frame number
    cv2.putText(frame, f'Synthetic Frame {frame_number}', (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    return frame

def add_test_overlay(frame: np.ndarray, frame_count: int, elapsed_time: float) -> np.ndarray:
    """Add test overlay information to frame"""
    overlay = frame.copy()
    h, w = frame.shape[:2]
    
    # Add semi-transparent background for text
    cv2.rectangle(overlay, (10, h-120), (300, h-10), (0, 0, 0), -1)
    
    # Add test information
    cv2.putText(overlay, f"Frame: {frame_count}", (20, h-90), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    cv2.putText(overlay, f"Time: {elapsed_time:.1f}s", (20, h-65), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    cv2.putText(overlay, f"FPS: {frame_count/elapsed_time:.1f}" if elapsed_time > 0 else "FPS: --", 
               (20, h-40), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    cv2.putText(overlay, "Press 'q' to quit", (20, h-15), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # Add stability indicator
    cv2.circle(overlay, (w-30, 30), 15, (0, 255, 0), -1)
    cv2.putText(overlay, "STABLE", (w-80, 40), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # Blend overlay
    alpha = 0.8
    return cv2.addWeighted(overlay, alpha, frame, 1-alpha, 0)

def test_multiple_windows():
    """Test multiple window management"""
    print_header("MULTIPLE WINDOW TEST")
    
    with get_window_manager() as wm:
        windows = ["Window_1", "Window_2", "Window_3"]
        
        # Create multiple windows
        for i, window_name in enumerate(windows):
            success = wm.create_window(window_name, 320, 240)
            print_status(f"Created {window_name}", success)
            
            if success:
                # Show different content in each window
                frame = create_synthetic_frame(320, 240, i * 10)
                cv2.putText(frame, window_name, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                wm.show_frame(window_name, frame)
        
        print("\n🎯 Multiple windows created. Press any key to continue...")
        wm.wait_key(0)
        
        # Test window responsiveness
        for window_name in windows:
            responsive = wm.is_window_open(window_name)
            print_status(f"{window_name} responsive", responsive)
        
        print_status("Multiple window test completed", True)

def main():
    """Main test function"""
    parser = argparse.ArgumentParser(description="Enhanced Camera Test Tool")
    parser.add_argument("--test", choices=["stability", "multiple", "all"], 
                       default="all", help="Test type to run")
    parser.add_argument("--duration", type=int, default=10, 
                       help="Test duration in seconds")
    
    args = parser.parse_args()
    
    print("🚀 Enhanced Camera Test Tool")
    print("Testing OpenCV window stability and camera functionality")
    
    success = True
    
    if args.test in ["stability", "all"]:
        success &= test_camera_window_stability()
    
    if args.test in ["multiple", "all"]:
        success &= test_multiple_windows()
    
    # Test window manager directly
    if args.test == "all":
        print_header("WINDOW MANAGER DIRECT TEST")
        from src.camera_window_manager import test_window_stability
        test_window_stability()
    
    print_header("TEST SUMMARY")
    if success:
        print("🎉 All camera window tests passed!")
        print("✅ OpenCV window stability confirmed")
        print("✅ Window lifecycle management working")
        print("✅ Multiple window support verified")
    else:
        print("❌ Some tests failed")
        print("💡 Check error messages above for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
