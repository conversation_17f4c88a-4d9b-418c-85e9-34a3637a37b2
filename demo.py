#!/usr/bin/env python3
"""
Demo script for ASL Sign Language Recognition System
Demonstrates the complete workflow from data collection to inference
"""

import os
import sys
import argparse
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from src.data_collection import ASLDataCollector
from src.preprocessing import ASLDataPreprocessor
from src.training import ASLTrainer
from src.inference import ASLInferenceEngine
from src.utils import (
    get_asl_alphabet, get_collection_summary, get_available_models,
    validate_camera_access, print_system_info, create_directory_structure,
    get_external_dataset_summary, validate_external_dataset_path
)


def demo_data_collection():
    """Demo data collection for a few letters"""
    print("="*60)
    print("DEMO: Data Collection")
    print("="*60)
    
    if not validate_camera_access():
        print("❌ Camera not available. Skipping data collection demo.")
        return False
    
    collector = ASLDataCollector()
    
    # Collect data for letters A, B, C (small samples for demo)
    demo_letters = ['A', 'B', 'C']
    samples_per_letter = 20
    
    print(f"Collecting {samples_per_letter} samples each for letters: {demo_letters}")
    print("Follow the on-screen instructions in the camera window.")
    
    for letter in demo_letters:
        print(f"\nCollecting data for letter '{letter}'...")
        success = collector.collect_data_for_label(letter, samples_per_letter)
        
        if not success:
            print(f"❌ Failed to collect data for letter '{letter}'")
            return False
        
        print(f"✅ Completed data collection for letter '{letter}'")
    
    # Show collection summary
    summary = collector.get_collection_summary()
    print("\nData Collection Summary:")
    for letter, count in summary.items():
        if count > 0:
            print(f"  Letter {letter}: {count} samples")
    
    return True


def demo_training():
    """Demo model training"""
    print("="*60)
    print("DEMO: Model Training")
    print("="*60)

    # Check available data sources
    manual_summary = get_collection_summary()
    manual_total = sum(manual_summary.values())

    external_dataset_path = "/home/<USER>/Documents/Sign Language Detector/asl_alphabet_dataset"
    external_valid, external_message = validate_external_dataset_path(external_dataset_path)
    external_total = 0

    if external_valid:
        external_summary = get_external_dataset_summary(external_dataset_path)
        external_total = sum(external_summary.values())

    # Determine which dataset to use
    if external_valid and external_total >= manual_total:
        print(f"Using external dataset: {external_total} samples")
        data_source = "external"
        total_samples = external_total
    elif manual_total > 0:
        print(f"Using manual collection: {manual_total} samples")
        data_source = "manual"
        total_samples = manual_total
    else:
        print("❌ No training data available")
        print("Please collect data first or ensure external dataset is accessible")
        return False

    if total_samples < 10:
        print(f"⚠️ Very limited data for training ({total_samples} samples)")
        print("Training will proceed but results may be poor")

    print(f"Training with {total_samples} total samples from {data_source} source")
    
    # Initialize trainer
    trainer = ASLTrainer()
    
    try:
        # Prepare data
        print("Preparing training data...")
        if data_source == "external":
            dataset = trainer.prepare_data(
                augment=True,
                augmentation_factor=2,
                data_source="external",
                external_dataset_path=external_dataset_path
            )
        else:
            dataset = trainer.prepare_data(augment=True, augmentation_factor=2)
        
        # Create a simple model for demo (fewer epochs)
        print("Creating model...")
        model = trainer.create_model(model_type="custom")
        
        # Train with fewer epochs for demo
        print("Training model (demo with 5 epochs)...")
        history = trainer.train_model(
            epochs=5,
            batch_size=16,
            model_name="demo_model"
        )
        
        # Evaluate model
        print("Evaluating model...")
        results = trainer.evaluate_model()
        
        print("✅ Training completed!")
        print(f"Test Accuracy: {results['test_accuracy']:.3f}")
        print(f"Test Loss: {results['test_loss']:.3f}")
        
        return True
    
    except Exception as e:
        print(f"❌ Training failed: {e}")
        return False


def demo_inference():
    """Demo real-time inference"""
    print("="*60)
    print("DEMO: Real-time Inference")
    print("="*60)
    
    if not validate_camera_access():
        print("❌ Camera not available. Skipping inference demo.")
        return False
    
    # Check for available models
    models = get_available_models()
    
    if not models:
        print("❌ No trained models available.")
        print("Please train a model first using demo_training()")
        return False
    
    # Use the most recent model
    latest_model = models[0]
    print(f"Using model: {latest_model['name']}")
    
    try:
        # Initialize inference engine
        engine = ASLInferenceEngine(
            model_path=latest_model['path'],
            confidence_threshold=0.7
        )
        
        print("Starting real-time inference...")
        print("Show ASL gestures to the camera. Press 'q' to quit.")
        
        # Run inference
        engine.run_inference()
        
        print("✅ Inference demo completed!")
        return True
    
    except Exception as e:
        print(f"❌ Inference failed: {e}")
        return False


def main():
    """Main demo function"""
    parser = argparse.ArgumentParser(description="ASL Recognition System Demo")
    parser.add_argument(
        "--mode",
        choices=["all", "collect", "train", "test", "info"],
        default="info",
        help="Demo mode to run"
    )
    
    args = parser.parse_args()
    
    # Print system info
    print_system_info()
    
    # Create directory structure
    create_directory_structure()
    
    if args.mode == "info":
        print("\n" + "="*60)
        print("ASL RECOGNITION SYSTEM DEMO")
        print("="*60)
        print("Available demo modes:")
        print("  --mode collect : Collect training data for demo letters")
        print("  --mode train   : Train a demo model")
        print("  --mode test    : Test real-time inference")
        print("  --mode all     : Run complete demo workflow")
        print("\nExample usage:")
        print("  python demo.py --mode collect")
        print("  python demo.py --mode train")
        print("  python demo.py --mode test")
        print("  python demo.py --mode all")
        
        # Show current status
        manual_summary = get_collection_summary()
        manual_total = sum(manual_summary.values())

        external_dataset_path = "/home/<USER>/Documents/Sign Language Detector/asl_alphabet_dataset"
        external_valid, _ = validate_external_dataset_path(external_dataset_path)
        external_total = 0
        if external_valid:
            external_summary = get_external_dataset_summary(external_dataset_path)
            external_total = sum(external_summary.values())

        models = get_available_models()

        print(f"\nCurrent Status:")
        print(f"  Manual collection samples: {manual_total}")
        print(f"  External dataset samples: {external_total}")
        print(f"  External dataset available: {external_valid}")
        print(f"  Available models: {len(models)}")
        print(f"  Camera available: {validate_camera_access()}")
    
    elif args.mode == "collect":
        demo_data_collection()
    
    elif args.mode == "train":
        demo_training()
    
    elif args.mode == "test":
        demo_inference()
    
    elif args.mode == "all":
        print("\n🚀 Running complete demo workflow...")
        
        # Step 1: Data collection
        if demo_data_collection():
            print("\n✅ Data collection completed")
        else:
            print("\n❌ Data collection failed. Stopping demo.")
            return
        
        # Step 2: Training
        if demo_training():
            print("\n✅ Training completed")
        else:
            print("\n❌ Training failed. Stopping demo.")
            return
        
        # Step 3: Inference
        if demo_inference():
            print("\n✅ Inference completed")
        else:
            print("\n❌ Inference failed.")
        
        print("\n🎉 Complete demo workflow finished!")


if __name__ == "__main__":
    main()
