#!/usr/bin/env python3
"""
Enhanced ASL Recognition Demo
Demonstrates the new visual overlay features
"""

import cv2
import numpy as np
import time
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Main demo function"""
    print("🎯 Enhanced ASL Recognition - Visual Overlay Demo")
    print("=" * 60)
    
    try:
        from src.inference import ASLInferenceEngine
        
        # Load the trained model
        model_path = 'data/models/external_dataset_demo.h5'
        if not os.path.exists(model_path):
            print(f"❌ Model not found: {model_path}")
            print("Please train a model first using the Streamlit interface.")
            return
        
        print("🔧 Loading ASL model...")
        engine = ASLInferenceEngine(model_path)
        print("✅ Model loaded successfully!")
        
        print("\n🎨 Enhanced Visual Features:")
        print("• Large letter display with confidence scores")
        print("• Prediction stability tracking")
        print("• Recent prediction history")
        print("• Performance statistics")
        print("• Gesture guidance")
        print("• Interactive controls")
        
        print("\n🎮 Controls:")
        print("• Q - Quit")
        print("• R - Reset statistics")
        print("• S - Save screenshot")
        print("• H - Show help")
        
        print("\n🚀 Starting enhanced real-time ASL recognition...")
        print("OpenCV window will open with full visual overlay system!")
        
        # Start the enhanced inference
        engine.run_inference(window_name="Enhanced ASL Recognition Demo")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed.")
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Please check the model path and camera connection.")
    
    print("\n🎉 Enhanced ASL recognition demo completed!")

if __name__ == "__main__":
    main()
