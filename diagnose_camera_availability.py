#!/usr/bin/env python3
"""
Comprehensive Camera Availability Diagnostic Tool
Diagnoses and resolves camera detection issues across all system components
"""

import sys
import os
import time
import cv2
import json
from pathlib import Path

# Add project path
sys.path.append('/home/<USER>/Documents/Sign Language Detector')

def print_header(title: str):
    """Print formatted header"""
    print("\n" + "=" * 70)
    print(f"🔍 {title}")
    print("=" * 70)

def print_status(message: str, success: bool):
    """Print status message"""
    status = "✅" if success else "❌"
    print(f"   {status} {message}")

def test_direct_opencv_camera():
    """Test direct OpenCV camera access"""
    print_header("DIRECT OPENCV CAMERA ACCESS TEST")
    
    working_cameras = []
    
    print("Testing camera indices 0-4...")
    for i in range(5):
        try:
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    h, w = frame.shape[:2]
                    working_cameras.append({
                        'index': i,
                        'resolution': f"{w}x{h}",
                        'working': True
                    })
                    print_status(f"Camera {i}: Working ({w}x{h})", True)
                else:
                    print_status(f"Camera {i}: Opened but no frame", False)
                cap.release()
            else:
                print_status(f"Camera {i}: Cannot open", False)
        except Exception as e:
            print_status(f"Camera {i}: Error - {e}", False)
    
    print(f"\n📊 Summary: {len(working_cameras)} working cameras found")
    return working_cameras

def test_camera_manager():
    """Test camera manager functionality"""
    print_header("CAMERA MANAGER TEST")
    
    try:
        from src.camera_manager import get_camera_manager
        
        # Initialize camera manager
        cm = get_camera_manager()
        print_status("Camera manager initialized", True)
        
        # Get current info
        info = cm.get_camera_info()
        print(f"\n📊 Current Camera Manager Status:")
        print(f"   Active Camera: {info.get('active_camera_index', 'None')}")
        print(f"   Camera Accessible: {info.get('camera_accessible', False)}")
        print(f"   Working Camera Count: {info.get('working_camera_count', 0)}")
        print(f"   Config File: {info.get('config_file', 'N/A')}")
        
        # Rescan cameras
        print("\n🔄 Rescanning cameras...")
        cameras = cm.scan_cameras()
        
        print(f"Found {len(cameras)} cameras:")
        for cam in cameras:
            print(f"   Camera {cam['index']}: {cam['resolution']} - {'Working' if cam['working'] else 'Not working'}")
        
        # Test active camera
        active = cm.get_active_camera()
        if active is not None:
            working = cm.test_camera(active)
            print_status(f"Active camera {active} test", working)
            return True, active, cameras
        else:
            print_status("No active camera available", False)
            return False, None, cameras
            
    except Exception as e:
        print_status(f"Camera manager error: {e}", False)
        return False, None, []

def test_utils_validation():
    """Test utils camera validation function"""
    print_header("UTILS CAMERA VALIDATION TEST")
    
    try:
        from src.utils import validate_camera_access
        
        # Test default validation
        print("Testing default camera validation...")
        result = validate_camera_access()
        print_status(f"Default camera validation", result)
        
        # Test specific indices
        print("\nTesting specific camera indices...")
        for i in range(3):
            result = validate_camera_access(i)
            print_status(f"Camera {i} validation", result)
        
        return validate_camera_access()
        
    except Exception as e:
        print_status(f"Utils validation error: {e}", False)
        return False

def test_inference_engine():
    """Test inference engine camera integration"""
    print_header("INFERENCE ENGINE CAMERA TEST")
    
    try:
        from src.inference import ASLInferenceEngine
        
        # Check if model exists
        model_path = 'data/models/validation_test_model.h5'
        if not os.path.exists(model_path):
            print_status("Model not found for inference test", False)
            return False
        
        # Initialize inference engine
        engine = ASLInferenceEngine(model_path)
        print_status("Inference engine initialized", True)
        
        # Test display capability
        display_ok = engine.test_display_capability()
        print_status("Display capability test", display_ok)
        
        return display_ok
        
    except Exception as e:
        print_status(f"Inference engine error: {e}", False)
        return False

def check_camera_configuration():
    """Check camera configuration files"""
    print_header("CAMERA CONFIGURATION CHECK")
    
    config_file = "config/camera_config.json"
    
    if os.path.exists(config_file):
        print_status(f"Configuration file exists: {config_file}", True)
        
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            print("\n📄 Configuration contents:")
            for key, value in config.items():
                print(f"   {key}: {value}")
            
            return config
            
        except Exception as e:
            print_status(f"Error reading configuration: {e}", False)
            return None
    else:
        print_status(f"Configuration file not found: {config_file}", False)
        return None

def fix_camera_configuration(working_cameras):
    """Fix camera configuration based on working cameras"""
    print_header("CAMERA CONFIGURATION FIX")
    
    if not working_cameras:
        print_status("No working cameras to configure", False)
        return False
    
    try:
        from src.camera_manager import get_camera_manager
        
        # Get camera manager and force rescan
        cm = get_camera_manager()
        cameras = cm.scan_cameras()
        
        if cameras:
            active_camera = cameras[0]['index']
            print_status(f"Camera configuration updated (active: {active_camera})", True)
            
            # Verify configuration
            info = cm.get_camera_info()
            print(f"\n📊 Updated Configuration:")
            print(f"   Active Camera: {info.get('active_camera_index')}")
            print(f"   Camera Accessible: {info.get('camera_accessible')}")
            print(f"   Working Camera Count: {info.get('working_camera_count')}")
            
            return True
        else:
            print_status("No cameras found during configuration fix", False)
            return False
            
    except Exception as e:
        print_status(f"Configuration fix error: {e}", False)
        return False

def test_streamlit_integration():
    """Test Streamlit app camera integration"""
    print_header("STREAMLIT INTEGRATION TEST")
    
    try:
        # Test the validation function that Streamlit uses
        from src.utils import validate_camera_access
        
        result = validate_camera_access()
        print_status("Streamlit camera validation", result)
        
        if result:
            print("✅ Streamlit should now show camera as available")
        else:
            print("❌ Streamlit will still show camera as not available")
        
        return result
        
    except Exception as e:
        print_status(f"Streamlit integration test error: {e}", False)
        return False

def run_comprehensive_diagnosis():
    """Run comprehensive camera availability diagnosis"""
    print("🚀 Comprehensive Camera Availability Diagnosis")
    print("Diagnosing camera detection issues across all system components")
    
    results = {}
    
    # Test 1: Direct OpenCV access
    working_cameras = test_direct_opencv_camera()
    results['direct_opencv'] = len(working_cameras) > 0
    
    # Test 2: Camera manager
    cm_success, active_camera, cm_cameras = test_camera_manager()
    results['camera_manager'] = cm_success
    
    # Test 3: Utils validation
    utils_success = test_utils_validation()
    results['utils_validation'] = utils_success
    
    # Test 4: Inference engine
    inference_success = test_inference_engine()
    results['inference_engine'] = inference_success
    
    # Test 5: Configuration check
    config = check_camera_configuration()
    results['configuration'] = config is not None
    
    # Test 6: Streamlit integration
    streamlit_success = test_streamlit_integration()
    results['streamlit_integration'] = streamlit_success
    
    # Summary and fixes
    print_header("DIAGNOSIS SUMMARY")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"Tests passed: {passed_tests}/{total_tests}")
    
    for test_name, success in results.items():
        print_status(test_name.replace('_', ' ').title(), success)
    
    # Apply fixes if needed
    if not results['camera_manager'] or not results['utils_validation']:
        print("\n🔧 Applying fixes...")
        fix_success = fix_camera_configuration(working_cameras)
        
        if fix_success:
            print("\n🔄 Re-testing after fixes...")
            
            # Re-test utils validation
            utils_success = test_utils_validation()
            print_status("Utils validation (after fix)", utils_success)
            
            # Re-test Streamlit integration
            streamlit_success = test_streamlit_integration()
            print_status("Streamlit integration (after fix)", streamlit_success)
    
    # Final recommendations
    print_header("RECOMMENDATIONS")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Camera availability should now be properly detected")
        print("✅ Streamlit app should show camera as available")
        print("✅ Real-time ASL recognition should be accessible")
    else:
        print("⚠️  Some issues detected:")
        
        if not results['direct_opencv']:
            print("   • No cameras detected at hardware level")
            print("   • Check camera hardware connection")
        
        if not results['camera_manager']:
            print("   • Camera manager not detecting cameras")
            print("   • Try restarting the application")
        
        if not results['utils_validation']:
            print("   • Utils validation function needs updating")
            print("   • Camera index mismatch possible")
        
        if not results['streamlit_integration']:
            print("   • Streamlit app may still show camera as unavailable")
            print("   • Restart Streamlit app to refresh")
    
    print("\n💡 Next steps:")
    print("   1. Restart Streamlit app if it's running")
    print("   2. Check the Home page for updated camera status")
    print("   3. Try the Real-time Inference section")
    
    return passed_tests == total_tests

def main():
    """Main diagnostic function"""
    success = run_comprehensive_diagnosis()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
