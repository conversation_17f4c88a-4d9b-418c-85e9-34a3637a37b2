#!/usr/bin/env python3
"""
Setup ASL Dataset for Training and Testing
Downloads and organizes ASL alphabet dataset
"""

import os
import sys
import requests
import zipfile
import shutil
from pathlib import Path
import time

def print_status(message: str, success: bool = True):
    """Print status message"""
    status = "✅" if success else "❌"
    print(f"{status} {message}")

def download_file(url: str, destination: str) -> bool:
    """Download file with progress"""
    try:
        print(f"📥 Downloading: {os.path.basename(destination)}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(destination, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r   Progress: {progress:.1f}%", end='', flush=True)
        
        print()  # New line after progress
        return True
        
    except Exception as e:
        print(f"\n❌ Download failed: {e}")
        return False

def create_sample_dataset():
    """Create a sample ASL dataset for testing"""
    print("🎯 Creating sample ASL dataset for testing...")
    
    # Create dataset structure
    dataset_path = Path("data/external/asl_alphabet_train")
    dataset_path.mkdir(parents=True, exist_ok=True)
    
    # Create sample classes (A-E for testing)
    sample_classes = ['A', 'B', 'C', 'D', 'E']
    
    try:
        import cv2
        import numpy as np
        
        for class_name in sample_classes:
            class_dir = dataset_path / class_name
            class_dir.mkdir(exist_ok=True)
            
            # Create 20 sample images per class
            for i in range(20):
                # Create a simple colored image as placeholder
                img = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
                
                # Add class letter to image
                cv2.putText(img, class_name, (50, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 3, (255, 255, 255), 3)
                
                # Save image
                img_path = class_dir / f"{class_name}_{i+1:03d}.jpg"
                cv2.imwrite(str(img_path), img)
            
            print_status(f"Created {class_name} class with 20 sample images")
        
        print_status(f"Sample dataset created at: {dataset_path}")
        return True
        
    except Exception as e:
        print_status(f"Error creating sample dataset: {e}", False)
        return False

def download_kaggle_dataset():
    """Download ASL dataset from Kaggle (if available)"""
    print("🌐 Attempting to download ASL dataset from Kaggle...")
    
    try:
        # Check if kaggle is available
        import kaggle
        
        # Download ASL alphabet dataset
        dataset_name = "grassknoted/asl-alphabet"
        download_path = "data/external"
        
        print(f"📥 Downloading {dataset_name}...")
        kaggle.api.dataset_download_files(
            dataset_name, 
            path=download_path, 
            unzip=True
        )
        
        print_status("Kaggle dataset downloaded successfully")
        return True
        
    except ImportError:
        print_status("Kaggle API not available", False)
        return False
    except Exception as e:
        print_status(f"Kaggle download failed: {e}", False)
        return False

def setup_dataset():
    """Main dataset setup function"""
    print("🚀 ASL Dataset Setup")
    print("=" * 50)
    
    # Check if dataset already exists
    dataset_path = Path("data/external/asl_alphabet_train")
    if dataset_path.exists() and any(dataset_path.iterdir()):
        print_status("Dataset already exists")
        
        # Count existing data
        class_count = len([d for d in dataset_path.iterdir() if d.is_dir()])
        total_images = sum(len(list(d.glob("*.jpg"))) + len(list(d.glob("*.png"))) 
                          for d in dataset_path.iterdir() if d.is_dir())
        
        print(f"   📊 Found {class_count} classes with {total_images} total images")
        
        if total_images > 100:  # Reasonable dataset size
            print_status("Existing dataset appears sufficient")
            return True
    
    # Try different download methods
    print("📥 Setting up ASL dataset...")
    
    # Method 1: Try Kaggle download
    if download_kaggle_dataset():
        return True
    
    # Method 2: Create sample dataset for testing
    print("📝 Creating sample dataset for testing...")
    if create_sample_dataset():
        print("\n💡 Sample dataset created for testing purposes.")
        print("   For production use, consider downloading a full ASL dataset.")
        return True
    
    print_status("Failed to set up dataset", False)
    return False

def validate_dataset():
    """Validate the setup dataset"""
    print("\n🔍 Validating dataset...")
    
    try:
        sys.path.append('/home/<USER>/Documents/Sign Language Detector')
        from src.utils import validate_external_dataset
        
        is_valid = validate_external_dataset()
        
        if is_valid:
            print_status("Dataset validation successful")
            
            # Get dataset summary
            from src.utils import get_external_dataset_summary
            summary = get_external_dataset_summary()
            
            print(f"   📊 Classes: {summary.get('num_classes', 0)}")
            print(f"   📊 Total Images: {summary.get('total_images', 0)}")
            
            return True
        else:
            print_status("Dataset validation failed", False)
            return False
            
    except Exception as e:
        print_status(f"Validation error: {e}", False)
        return False

def main():
    """Main function"""
    print("🎯 ASL Dataset Setup Tool")
    print("This tool will set up the ASL alphabet dataset for training")
    print()
    
    # Setup dataset
    if setup_dataset():
        # Validate dataset
        if validate_dataset():
            print("\n🎉 Dataset setup completed successfully!")
            print("\n🚀 Next steps:")
            print("   1. Run: python3 validate_asl_system.py")
            print("   2. Use Streamlit interface: http://localhost:8507")
            print("   3. Train model with external dataset")
        else:
            print("\n⚠️  Dataset setup completed but validation failed")
            print("   Check the dataset structure and try again")
    else:
        print("\n❌ Dataset setup failed")
        print("   Please check internet connection and try again")

if __name__ == "__main__":
    main()
