"""
ASL Sign Language Recognition System
Source package initialization
"""

__version__ = "1.0.0"
__author__ = "ASL Recognition Team"
__description__ = "Real-time American Sign Language Recognition System"

# Import main classes for easy access
from .data_collection import ASLDataCollector
from .preprocessing import ASLDataPreprocessor
from .model import ASLModel
from .training import ASLTrainer
from .inference import ASLInferenceEngine
from .utils import (
    get_asl_alphabet,
    get_collection_summary,
    get_available_models,
    validate_camera_access,
    print_system_info
)

__all__ = [
    'ASLDataCollector',
    'ASLDataPreprocessor', 
    'ASLModel',
    'ASLTrainer',
    'ASLInferenceEngine',
    'get_asl_alphabet',
    'get_collection_summary',
    'get_available_models',
    'validate_camera_access',
    'print_system_info'
]
