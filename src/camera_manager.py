#!/usr/bin/env python3
"""
Simple Camera Manager for ASL Recognition System
Provides basic camera detection and management functionality
"""

import cv2
import json
import os
import time
import logging
from typing import Dict, List, Optional, Any

class CameraManager:
    """Simple camera manager for testing purposes"""
    
    def __init__(self):
        """Initialize camera manager"""
        self.config_file = "config/camera_config.json"
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # Ensure config directory exists
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        # Load or create configuration
        self.config = self._load_config()
        
    def _setup_logging(self):
        """Setup logging"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _load_config(self) -> Dict[str, Any]:
        """Load camera configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"Failed to load config: {e}")
        
        # Return default config
        return {
            "active_camera_index": None,
            "available_cameras": [],
            "last_scan_time": 0,
            "last_updated": None
        }
    
    def _save_config(self):
        """Save camera configuration"""
        try:
            self.config["last_updated"] = time.strftime("%Y-%m-%dT%H:%M:%S")
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
    
    def scan_cameras(self, max_cameras: int = 10) -> List[Dict[str, Any]]:
        """
        Scan for available cameras
        
        Args:
            max_cameras: Maximum number of camera indices to test
            
        Returns:
            List of available camera information
        """
        self.logger.info(f"Scanning cameras (indices 0-{max_cameras-1})...")
        
        available_cameras = []
        
        for i in range(max_cameras):
            try:
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        h, w = frame.shape[:2]
                        camera_info = {
                            "index": i,
                            "working": True,
                            "resolution": f"{w}x{h}",
                            "test_time": time.time()
                        }
                        available_cameras.append(camera_info)
                        self.logger.info(f"Camera {i}: Working ({w}x{h})")
                    else:
                        self.logger.info(f"Camera {i}: Not working (no frame)")
                cap.release()
            except Exception as e:
                self.logger.debug(f"Camera {i}: Error - {e}")
        
        self.config["available_cameras"] = available_cameras
        self.config["last_scan_time"] = time.time()
        
        # Set active camera to first working camera
        if available_cameras and self.config["active_camera_index"] is None:
            self.config["active_camera_index"] = available_cameras[0]["index"]
        
        self._save_config()
        self.logger.info(f"Camera scan complete: {len(available_cameras)} working cameras found")
        
        return available_cameras
    
    def get_active_camera(self) -> Optional[int]:
        """
        Get active camera index
        
        Returns:
            Active camera index or None if no camera available
        """
        return self.config.get("active_camera_index")
    
    def get_camera_info(self) -> Dict[str, Any]:
        """
        Get comprehensive camera information
        
        Returns:
            Dictionary with camera system information
        """
        # Scan cameras if not done recently
        if time.time() - self.config.get("last_scan_time", 0) > 300:  # 5 minutes
            self.scan_cameras()
        
        available_cameras = self.config.get("available_cameras", [])
        active_camera = self.config.get("active_camera_index")
        
        return {
            "active_camera_index": active_camera,
            "camera_accessible": len(available_cameras) > 0,
            "working_camera_count": len(available_cameras),
            "available_cameras": available_cameras,
            "config_file": self.config_file
        }
    
    def test_camera(self, camera_index: int) -> bool:
        """
        Test if a specific camera is working
        
        Args:
            camera_index: Camera index to test
            
        Returns:
            True if camera is working, False otherwise
        """
        try:
            cap = cv2.VideoCapture(camera_index)
            if not cap.isOpened():
                return False
            
            ret, frame = cap.read()
            cap.release()
            
            return ret and frame is not None
            
        except Exception:
            return False


# Global camera manager instance
_camera_manager = None

def get_camera_manager() -> CameraManager:
    """
    Get the global camera manager instance
    
    Returns:
        CameraManager instance
    """
    global _camera_manager
    if _camera_manager is None:
        _camera_manager = CameraManager()
    return _camera_manager


if __name__ == "__main__":
    # Test camera manager
    print("🔧 Testing Camera Manager...")
    
    cm = get_camera_manager()
    
    # Scan for cameras
    cameras = cm.scan_cameras()
    print(f"Found {len(cameras)} working cameras")
    
    # Get camera info
    info = cm.get_camera_info()
    print(f"Camera info: {info}")
    
    # Test active camera
    active = cm.get_active_camera()
    if active is not None:
        working = cm.test_camera(active)
        print(f"Active camera {active}: {'Working' if working else 'Not working'}")
    else:
        print("No active camera available")
