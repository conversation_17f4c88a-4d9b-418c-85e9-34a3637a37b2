"""
Camera Management System for ASL Recognition
Provides consistent, persistent camera detection and configuration
"""

import cv2
import json
import os
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class CameraInfo:
    """Camera information structure"""
    index: int
    resolution: str
    fps: float
    working: bool
    last_tested: str
    device_name: str = "Unknown"
    priority: int = 0  # Lower number = higher priority

class CameraManager:
    """
    Centralized camera management system with persistence and consistency
    """
    
    def __init__(self, config_file: str = "config/camera_config.json"):
        """
        Initialize camera manager
        
        Args:
            config_file: Path to camera configuration file
        """
        self.config_file = config_file
        self.config_dir = os.path.dirname(config_file)
        self.logger = self._setup_logging()
        
        # Ensure config directory exists
        if self.config_dir and not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
        
        # Camera state
        self.active_camera_index: Optional[int] = None
        self.available_cameras: List[CameraInfo] = []
        self.last_scan_time: Optional[float] = None
        self.scan_interval = 30  # Re-scan every 30 seconds if needed
        
        # Load existing configuration
        self.load_configuration()
        
        # Validate and update camera status
        self.refresh_camera_status()
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for camera manager"""
        logger = logging.getLogger('CameraManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_configuration(self) -> None:
        """Load camera configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                
                self.active_camera_index = config.get('active_camera_index')
                
                # Load camera info
                cameras_data = config.get('available_cameras', [])
                self.available_cameras = [
                    CameraInfo(**camera_data) for camera_data in cameras_data
                ]
                
                self.last_scan_time = config.get('last_scan_time')
                
                self.logger.info(f"Loaded configuration: active camera {self.active_camera_index}")
            else:
                self.logger.info("No existing configuration found, will create new one")
        
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            self._reset_configuration()
    
    def save_configuration(self) -> None:
        """Save camera configuration to file"""
        try:
            config = {
                'active_camera_index': self.active_camera_index,
                'available_cameras': [asdict(camera) for camera in self.available_cameras],
                'last_scan_time': time.time(),
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            self.logger.info(f"Configuration saved: active camera {self.active_camera_index}")
        
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
    
    def _reset_configuration(self) -> None:
        """Reset configuration to defaults"""
        self.active_camera_index = None
        self.available_cameras = []
        self.last_scan_time = None
    
    def scan_cameras(self, max_index: int = 10, force_rescan: bool = False) -> List[CameraInfo]:
        """
        Scan for available cameras
        
        Args:
            max_index: Maximum camera index to check
            force_rescan: Force rescan even if recently scanned
            
        Returns:
            List of available cameras
        """
        current_time = time.time()
        
        # Check if we need to rescan
        if (not force_rescan and 
            self.last_scan_time and 
            current_time - self.last_scan_time < self.scan_interval and
            self.available_cameras):
            self.logger.info("Using cached camera scan results")
            return self.available_cameras
        
        self.logger.info(f"Scanning cameras (indices 0-{max_index-1})...")
        cameras = []
        
        for i in range(max_index):
            try:
                self.logger.debug(f"Testing camera index {i}")
                cap = cv2.VideoCapture(i)
                
                if cap.isOpened():
                    # Try to read a frame
                    ret, frame = cap.read()
                    
                    if ret and frame is not None:
                        # Get camera properties
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        fps = cap.get(cv2.CAP_PROP_FPS)
                        
                        camera_info = CameraInfo(
                            index=i,
                            resolution=f"{width}x{height}",
                            fps=fps if fps > 0 else 30.0,
                            working=True,
                            last_tested=datetime.now().isoformat(),
                            device_name=f"Camera {i}",
                            priority=i  # Lower index = higher priority
                        )
                        
                        cameras.append(camera_info)
                        self.logger.info(f"✅ Camera {i}: Working ({width}x{height}, {fps:.1f} FPS)")
                    else:
                        self.logger.debug(f"❌ Camera {i}: Opens but no frame")
                
                cap.release()
                
            except Exception as e:
                self.logger.debug(f"❌ Camera {i}: Error - {e}")
        
        # Sort cameras by priority (lower index first)
        cameras.sort(key=lambda x: x.priority)
        
        self.available_cameras = cameras
        self.last_scan_time = current_time
        
        self.logger.info(f"Camera scan complete: {len(cameras)} working cameras found")
        return cameras
    
    def get_active_camera(self) -> Optional[int]:
        """
        Get the currently active camera index
        
        Returns:
            Active camera index or None if no camera available
        """
        # If we have an active camera, verify it's still working
        if self.active_camera_index is not None:
            if self._verify_camera(self.active_camera_index):
                return self.active_camera_index
            else:
                self.logger.warning(f"Previously active camera {self.active_camera_index} no longer working")
                self.active_camera_index = None
        
        # Find the best available camera
        self.refresh_camera_status()
        
        if self.available_cameras:
            # Use the highest priority (lowest index) working camera
            for camera in self.available_cameras:
                if camera.working and self._verify_camera(camera.index):
                    self.active_camera_index = camera.index
                    self.save_configuration()
                    self.logger.info(f"Selected camera {self.active_camera_index} as active")
                    return self.active_camera_index
        
        self.logger.error("No working cameras found")
        return None
    
    def _verify_camera(self, camera_index: int) -> bool:
        """
        Verify that a specific camera is working
        
        Args:
            camera_index: Camera index to verify
            
        Returns:
            True if camera is working, False otherwise
        """
        try:
            cap = cv2.VideoCapture(camera_index)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                return ret and frame is not None
            return False
        except Exception:
            return False
    
    def refresh_camera_status(self) -> None:
        """Refresh the status of all cameras"""
        self.scan_cameras(force_rescan=True)
        
        # Update working status for existing cameras
        for camera in self.available_cameras:
            camera.working = self._verify_camera(camera.index)
            camera.last_tested = datetime.now().isoformat()
        
        self.save_configuration()
    
    def set_active_camera(self, camera_index: int) -> bool:
        """
        Set a specific camera as active
        
        Args:
            camera_index: Camera index to set as active
            
        Returns:
            True if successful, False otherwise
        """
        if self._verify_camera(camera_index):
            self.active_camera_index = camera_index
            self.save_configuration()
            self.logger.info(f"Manually set camera {camera_index} as active")
            return True
        else:
            self.logger.error(f"Cannot set camera {camera_index} as active - not working")
            return False
    
    def get_camera_info(self) -> Dict:
        """
        Get comprehensive camera information
        
        Returns:
            Dictionary with camera status and information
        """
        active_camera = self.get_active_camera()
        
        return {
            'active_camera_index': active_camera,
            'camera_accessible': active_camera is not None,
            'available_cameras': [asdict(camera) for camera in self.available_cameras],
            'working_camera_count': len([c for c in self.available_cameras if c.working]),
            'last_scan_time': self.last_scan_time,
            'config_file': self.config_file
        }
    
    def get_camera_status_summary(self) -> str:
        """
        Get a human-readable camera status summary
        
        Returns:
            Status summary string
        """
        active_camera = self.get_active_camera()
        working_count = len([c for c in self.available_cameras if c.working])
        
        if active_camera is not None:
            return f"✅ Camera {active_camera} Active ({working_count} total available)"
        elif working_count > 0:
            return f"⚠️ {working_count} cameras available, none active"
        else:
            return "❌ No cameras available"

# Global camera manager instance
_camera_manager = None

def get_camera_manager() -> CameraManager:
    """Get the global camera manager instance"""
    global _camera_manager
    if _camera_manager is None:
        _camera_manager = CameraManager()
    return _camera_manager

def get_active_camera() -> Optional[int]:
    """Convenience function to get active camera index"""
    return get_camera_manager().get_active_camera()

def get_camera_info() -> Dict:
    """Convenience function to get camera information"""
    return get_camera_manager().get_camera_info()
