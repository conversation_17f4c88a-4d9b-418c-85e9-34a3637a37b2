#!/usr/bin/env python3
"""
Enhanced Camera Window Manager for OpenCV
Provides robust window lifecycle management and stability fixes
"""

import cv2
import time
import threading
from typing import Optional, Dict, Any, Callable
import logging

class CameraWindowManager:
    """
    Enhanced camera window manager with robust lifecycle management
    Fixes OpenCV window stability issues and provides persistent window handling
    """
    
    def __init__(self):
        """Initialize the camera window manager"""
        self.windows = {}  # Track active windows
        self.window_properties = {}  # Store window properties
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # Window management settings
        self.default_window_flags = cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO
        self.window_wait_key_delay = 1  # ms
        self.window_creation_delay = 0.1  # seconds
        
    def _setup_logging(self):
        """Setup logging for window manager"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def create_window(self, window_name: str, width: int = 640, height: int = 480, 
                     resizable: bool = True, always_on_top: bool = False) -> bool:
        """
        Create a new OpenCV window with enhanced stability
        
        Args:
            window_name: Name of the window
            width: Window width
            height: Window height
            resizable: Whether window should be resizable
            always_on_top: Whether window should stay on top
            
        Returns:
            True if window created successfully, False otherwise
        """
        try:
            # Check if window already exists
            if window_name in self.windows:
                self.logger.warning(f"Window '{window_name}' already exists")
                return True
            
            # Determine window flags
            flags = cv2.WINDOW_NORMAL if resizable else cv2.WINDOW_AUTOSIZE
            if always_on_top:
                flags |= cv2.WINDOW_KEEPRATIO
            
            # Create the window
            cv2.namedWindow(window_name, flags)
            
            # Set window properties
            if resizable:
                cv2.resizeWindow(window_name, width, height)
            
            # Store window information
            self.windows[window_name] = {
                'created_time': time.time(),
                'width': width,
                'height': height,
                'resizable': resizable,
                'flags': flags,
                'frame_count': 0,
                'last_update': time.time()
            }
            
            # Small delay to ensure window is properly created
            time.sleep(self.window_creation_delay)
            
            self.logger.info(f"Window '{window_name}' created successfully ({width}x{height})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create window '{window_name}': {e}")
            return False
    
    def show_frame(self, window_name: str, frame, auto_create: bool = True) -> bool:
        """
        Display frame in window with enhanced error handling
        
        Args:
            window_name: Name of the window
            frame: Frame to display
            auto_create: Whether to auto-create window if it doesn't exist
            
        Returns:
            True if frame displayed successfully, False otherwise
        """
        try:
            # Auto-create window if needed
            if window_name not in self.windows and auto_create:
                if frame is not None:
                    h, w = frame.shape[:2]
                    self.create_window(window_name, w, h)
                else:
                    self.create_window(window_name)
            
            # Check if window exists
            if window_name not in self.windows:
                self.logger.error(f"Window '{window_name}' does not exist")
                return False
            
            # Display frame
            cv2.imshow(window_name, frame)
            
            # Update window statistics
            self.windows[window_name]['frame_count'] += 1
            self.windows[window_name]['last_update'] = time.time()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to show frame in window '{window_name}': {e}")
            return False
    
    def wait_key(self, delay: int = None) -> int:
        """
        Enhanced waitKey with proper delay handling
        
        Args:
            delay: Wait delay in milliseconds (uses default if None)
            
        Returns:
            Key code pressed
        """
        if delay is None:
            delay = self.window_wait_key_delay
        
        return cv2.waitKey(delay) & 0xFF
    
    def is_window_open(self, window_name: str) -> bool:
        """
        Check if window is still open and responsive
        
        Args:
            window_name: Name of the window to check
            
        Returns:
            True if window is open and responsive, False otherwise
        """
        try:
            if window_name not in self.windows:
                return False
            
            # Try to get window property to test if window is still alive
            prop = cv2.getWindowProperty(window_name, cv2.WND_PROP_VISIBLE)
            return prop >= 0
            
        except Exception:
            return False
    
    def close_window(self, window_name: str) -> bool:
        """
        Close a specific window
        
        Args:
            window_name: Name of the window to close
            
        Returns:
            True if window closed successfully, False otherwise
        """
        try:
            if window_name in self.windows:
                cv2.destroyWindow(window_name)
                
                # Remove from tracking
                window_info = self.windows.pop(window_name)
                
                self.logger.info(f"Window '{window_name}' closed (displayed {window_info['frame_count']} frames)")
                return True
            else:
                self.logger.warning(f"Window '{window_name}' not found for closing")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to close window '{window_name}': {e}")
            return False
    
    def close_all_windows(self) -> bool:
        """
        Close all managed windows
        
        Returns:
            True if all windows closed successfully, False otherwise
        """
        try:
            success = True
            window_names = list(self.windows.keys())
            
            for window_name in window_names:
                if not self.close_window(window_name):
                    success = False
            
            # Ensure all OpenCV windows are destroyed
            cv2.destroyAllWindows()
            
            # Small delay to ensure cleanup
            time.sleep(0.1)
            
            self.logger.info("All windows closed")
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to close all windows: {e}")
            return False
    
    def get_window_info(self, window_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a window
        
        Args:
            window_name: Name of the window
            
        Returns:
            Window information dictionary or None if window doesn't exist
        """
        return self.windows.get(window_name)
    
    def get_all_windows(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all managed windows
        
        Returns:
            Dictionary of all window information
        """
        return self.windows.copy()
    
    def cleanup_dead_windows(self) -> int:
        """
        Clean up windows that are no longer responsive
        
        Returns:
            Number of dead windows cleaned up
        """
        dead_windows = []
        
        for window_name in self.windows:
            if not self.is_window_open(window_name):
                dead_windows.append(window_name)
        
        for window_name in dead_windows:
            self.logger.warning(f"Cleaning up dead window: {window_name}")
            self.windows.pop(window_name, None)
        
        return len(dead_windows)
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - cleanup all windows"""
        self.close_all_windows()


# Global window manager instance
_window_manager = None

def get_window_manager() -> CameraWindowManager:
    """
    Get the global window manager instance
    
    Returns:
        CameraWindowManager instance
    """
    global _window_manager
    if _window_manager is None:
        _window_manager = CameraWindowManager()
    return _window_manager


def test_window_stability():
    """
    Test window stability and lifecycle management
    """
    print("🔧 Testing OpenCV Window Stability...")
    
    with get_window_manager() as wm:
        # Test 1: Basic window creation
        print("Test 1: Basic window creation")
        success = wm.create_window("test_window", 640, 480)
        print(f"   Window creation: {'✅ Success' if success else '❌ Failed'}")
        
        # Test 2: Frame display
        print("Test 2: Frame display")
        import numpy as np
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        test_frame[:] = (50, 100, 150)  # Blue background
        cv2.putText(test_frame, 'Window Stability Test', (150, 240), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        success = wm.show_frame("test_window", test_frame)
        print(f"   Frame display: {'✅ Success' if success else '❌ Failed'}")
        
        # Test 3: Window persistence
        print("Test 3: Window persistence (3 seconds)")
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 3.0:
            # Update frame with counter
            current_frame = test_frame.copy()
            cv2.putText(current_frame, f'Frame: {frame_count}', (250, 300), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            
            wm.show_frame("test_window", current_frame)
            key = wm.wait_key(30)
            
            if key == ord('q'):
                break
                
            frame_count += 1
        
        print(f"   Window persistence: ✅ Success ({frame_count} frames)")
        
        # Test 4: Window responsiveness
        print("Test 4: Window responsiveness")
        is_responsive = wm.is_window_open("test_window")
        print(f"   Window responsive: {'✅ Yes' if is_responsive else '❌ No'}")
        
        # Test 5: Multiple window creation
        print("Test 5: Multiple window creation")
        success2 = wm.create_window("test_window_2", 320, 240)
        print(f"   Second window: {'✅ Success' if success2 else '❌ Failed'}")
        
        if success2:
            small_frame = cv2.resize(test_frame, (320, 240))
            wm.show_frame("test_window_2", small_frame)
            wm.wait_key(1000)  # Show for 1 second
        
        print("✅ Window stability test completed")


if __name__ == "__main__":
    test_window_stability()
