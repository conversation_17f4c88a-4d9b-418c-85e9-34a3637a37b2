"""
Data Collection Module for ASL Sign Language Recognition
Captures webcam feed and collects labeled training images for ASL alphabet gestures
"""

import cv2
import mediapipe as mp
import numpy as np
import os
import time
from typing import Tuple, Optional
import json


class ASLDataCollector:
    """Collects training data for ASL alphabet recognition"""
    
    def __init__(self, data_dir: str = "data/raw"):
        """
        Initialize the data collector
        
        Args:
            data_dir: Directory to save collected images
        """
        self.data_dir = data_dir
        self.mp_hands = mp.solutions.hands
        self.mp_drawing = mp.solutions.drawing_utils
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        # ASL alphabet labels
        self.asl_labels = [chr(i) for i in range(ord('A'), ord('Z') + 1)]
        
        # Create directories for each label
        self._create_directories()
        
        # Statistics
        self.collection_stats = self._load_stats()
    
    def _create_directories(self):
        """Create directories for each ASL letter"""
        os.makedirs(self.data_dir, exist_ok=True)
        for label in self.asl_labels:
            label_dir = os.path.join(self.data_dir, label)
            os.makedirs(label_dir, exist_ok=True)
    
    def _load_stats(self) -> dict:
        """Load collection statistics"""
        stats_file = os.path.join(self.data_dir, "collection_stats.json")
        if os.path.exists(stats_file):
            with open(stats_file, 'r') as f:
                return json.load(f)
        return {label: 0 for label in self.asl_labels}
    
    def _save_stats(self):
        """Save collection statistics"""
        stats_file = os.path.join(self.data_dir, "collection_stats.json")
        with open(stats_file, 'w') as f:
            json.dump(self.collection_stats, f, indent=2)
    
    def extract_hand_region(self, image: np.ndarray, landmarks) -> Optional[np.ndarray]:
        """
        Extract hand region from image using landmarks
        
        Args:
            image: Input image
            landmarks: MediaPipe hand landmarks
            
        Returns:
            Cropped hand region or None if extraction fails
        """
        if not landmarks:
            return None
        
        h, w, _ = image.shape
        
        # Get bounding box coordinates
        x_coords = [landmark.x * w for landmark in landmarks.landmark]
        y_coords = [landmark.y * h for landmark in landmarks.landmark]
        
        x_min, x_max = int(min(x_coords)), int(max(x_coords))
        y_min, y_max = int(min(y_coords)), int(max(y_coords))
        
        # Add padding
        padding = 30
        x_min = max(0, x_min - padding)
        y_min = max(0, y_min - padding)
        x_max = min(w, x_max + padding)
        y_max = min(h, y_max + padding)
        
        # Extract and resize hand region
        hand_region = image[y_min:y_max, x_min:x_max]
        if hand_region.size > 0:
            hand_region = cv2.resize(hand_region, (224, 224))
            return hand_region
        
        return None
    
    def collect_data_for_label(self, label: str, target_samples: int = 100) -> bool:
        """
        Collect training data for a specific ASL letter
        
        Args:
            label: ASL letter to collect data for
            target_samples: Number of samples to collect
            
        Returns:
            True if collection was successful
        """
        if label not in self.asl_labels:
            print(f"Invalid label: {label}")
            return False
        
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("Error: Could not open webcam")
            return False
        
        label_dir = os.path.join(self.data_dir, label)
        current_count = len([f for f in os.listdir(label_dir) if f.endswith('.jpg')])
        
        print(f"Collecting data for letter '{label}'")
        print(f"Current samples: {current_count}, Target: {target_samples}")
        print("Press 's' to save sample, 'q' to quit")
        
        while current_count < target_samples:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip frame horizontally for mirror effect
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process frame with MediaPipe
            results = self.hands.process(rgb_frame)
            
            # Draw landmarks and bounding box
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # Draw landmarks
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS
                    )
                    
                    # Extract hand region
                    hand_region = self.extract_hand_region(frame, hand_landmarks)
                    
                    if hand_region is not None:
                        # Show hand region in corner
                        resized_hand = cv2.resize(hand_region, (150, 150))
                        frame[10:160, 10:160] = resized_hand
            
            # Add text overlay
            cv2.putText(frame, f"Letter: {label}", (10, 200), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Samples: {current_count}/{target_samples}", 
                       (10, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, "Press 's' to save, 'q' to quit", 
                       (10, 280), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow('ASL Data Collection', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('s'):
                # Save sample if hand is detected
                if results.multi_hand_landmarks:
                    hand_landmarks = results.multi_hand_landmarks[0]
                    hand_region = self.extract_hand_region(frame, hand_landmarks)
                    
                    if hand_region is not None:
                        filename = f"{label}_{current_count:04d}.jpg"
                        filepath = os.path.join(label_dir, filename)
                        cv2.imwrite(filepath, hand_region)
                        current_count += 1
                        self.collection_stats[label] = current_count
                        print(f"Saved sample {current_count}")
                        
                        # Brief pause to avoid duplicate saves
                        time.sleep(0.5)
                    else:
                        print("No hand detected, sample not saved")
                else:
                    print("No hand detected, sample not saved")
            
            elif key == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        self._save_stats()
        
        print(f"Collection complete for letter '{label}': {current_count} samples")
        return True
    
    def get_collection_summary(self) -> dict:
        """Get summary of collected data"""
        summary = {}
        for label in self.asl_labels:
            label_dir = os.path.join(self.data_dir, label)
            if os.path.exists(label_dir):
                count = len([f for f in os.listdir(label_dir) if f.endswith('.jpg')])
                summary[label] = count
            else:
                summary[label] = 0
        return summary


if __name__ == "__main__":
    # Example usage
    collector = ASLDataCollector()
    
    # Collect data for letter 'A'
    collector.collect_data_for_label('A', target_samples=50)
    
    # Print summary
    summary = collector.get_collection_summary()
    print("\nCollection Summary:")
    for letter, count in summary.items():
        print(f"Letter {letter}: {count} samples")
