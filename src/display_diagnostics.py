"""
Display Environment Diagnostics for ASL Recognition System
Comprehensive testing of OpenCV window display capabilities
"""

import cv2
import numpy as np
import os
import sys
import time
import subprocess
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging

@dataclass
class DisplayEnvironment:
    """Display environment information"""
    display_server: str
    display_variable: Optional[str]
    wayland_display: Optional[str]
    session_type: Optional[str]
    desktop_session: Optional[str]
    x11_forwarding: bool
    opencv_gui_support: bool
    qt_platform: Optional[str]

@dataclass
class WindowTestResult:
    """Result of OpenCV window test"""
    test_name: str
    success: bool
    error_message: Optional[str]
    duration_ms: float
    window_created: bool
    frame_displayed: bool
    user_interaction: bool

class DisplayDiagnostics:
    """
    Comprehensive display environment diagnostics and OpenCV window testing
    """
    
    def __init__(self):
        """Initialize display diagnostics"""
        self.logger = self._setup_logging()
        self.environment = self._detect_environment()
        self.test_results: List[WindowTestResult] = []
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for diagnostics"""
        logger = logging.getLogger('DisplayDiagnostics')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _detect_environment(self) -> DisplayEnvironment:
        """Detect current display environment"""
        # Get environment variables
        display_var = os.environ.get('DISPLAY')
        wayland_display = os.environ.get('WAYLAND_DISPLAY')
        session_type = os.environ.get('XDG_SESSION_TYPE')
        desktop_session = os.environ.get('DESKTOP_SESSION')
        qt_platform = os.environ.get('QT_QPA_PLATFORM')
        
        # Determine display server
        if wayland_display and session_type == 'wayland':
            display_server = 'wayland'
        elif display_var:
            display_server = 'x11'
        else:
            display_server = 'unknown'
        
        # Check X11 forwarding (for SSH)
        x11_forwarding = bool(display_var and display_var.startswith(':'))
        
        # Test basic OpenCV GUI support
        opencv_gui_support = self._test_opencv_gui_support()
        
        return DisplayEnvironment(
            display_server=display_server,
            display_variable=display_var,
            wayland_display=wayland_display,
            session_type=session_type,
            desktop_session=desktop_session,
            x11_forwarding=x11_forwarding,
            opencv_gui_support=opencv_gui_support,
            qt_platform=qt_platform
        )
    
    def _test_opencv_gui_support(self) -> bool:
        """Test if OpenCV has GUI support compiled"""
        try:
            # Try to create a simple window
            test_img = np.zeros((100, 100, 3), dtype=np.uint8)
            cv2.namedWindow('test_gui_support', cv2.WINDOW_NORMAL)
            cv2.imshow('test_gui_support', test_img)
            cv2.waitKey(1)
            cv2.destroyWindow('test_gui_support')
            return True
        except Exception:
            return False
    
    def run_comprehensive_diagnostics(self) -> Dict:
        """Run comprehensive display diagnostics"""
        self.logger.info("🔍 Starting comprehensive display diagnostics...")
        
        # Environment detection
        env_info = self._get_environment_info()
        
        # OpenCV window tests
        window_tests = self._run_window_tests()
        
        # Camera display tests
        camera_tests = self._run_camera_tests()
        
        # Recommendations
        recommendations = self._generate_recommendations()
        
        results = {
            'environment': env_info,
            'window_tests': window_tests,
            'camera_tests': camera_tests,
            'recommendations': recommendations,
            'overall_status': self._determine_overall_status()
        }
        
        self.logger.info("✅ Display diagnostics completed")
        return results
    
    def _get_environment_info(self) -> Dict:
        """Get detailed environment information"""
        return {
            'display_server': self.environment.display_server,
            'display_variable': self.environment.display_variable,
            'wayland_display': self.environment.wayland_display,
            'session_type': self.environment.session_type,
            'desktop_session': self.environment.desktop_session,
            'x11_forwarding': self.environment.x11_forwarding,
            'opencv_gui_support': self.environment.opencv_gui_support,
            'qt_platform': self.environment.qt_platform,
            'opencv_version': cv2.__version__,
            'python_version': sys.version,
            'platform': sys.platform
        }
    
    def _run_window_tests(self) -> List[Dict]:
        """Run comprehensive OpenCV window tests"""
        tests = [
            ('basic_window', self._test_basic_window),
            ('image_display', self._test_image_display),
            ('window_properties', self._test_window_properties),
            ('multiple_windows', self._test_multiple_windows),
            ('window_interaction', self._test_window_interaction)
        ]
        
        results = []
        for test_name, test_func in tests:
            self.logger.info(f"Running test: {test_name}")
            try:
                result = test_func()
                results.append(result)
                # Convert dict to WindowTestResult for consistency
                if isinstance(result, dict):
                    test_result = WindowTestResult(**result)
                else:
                    test_result = result
                self.test_results.append(test_result)
            except Exception as e:
                error_result = WindowTestResult(
                    test_name=test_name,
                    success=False,
                    error_message=str(e),
                    duration_ms=0,
                    window_created=False,
                    frame_displayed=False,
                    user_interaction=False
                )
                results.append(error_result.__dict__)
                self.test_results.append(error_result)
        
        return results
    
    def _test_basic_window(self) -> Dict:
        """Test basic window creation"""
        start_time = time.time()
        
        try:
            cv2.namedWindow('basic_test', cv2.WINDOW_NORMAL)
            window_created = True
            
            # Create test image
            test_img = np.zeros((300, 400, 3), dtype=np.uint8)
            test_img[:] = (50, 100, 150)  # Blue background
            
            cv2.imshow('basic_test', test_img)
            cv2.waitKey(100)  # Short wait
            
            frame_displayed = True
            cv2.destroyWindow('basic_test')
            
            duration = (time.time() - start_time) * 1000
            
            return WindowTestResult(
                test_name='basic_window',
                success=True,
                error_message=None,
                duration_ms=duration,
                window_created=window_created,
                frame_displayed=frame_displayed,
                user_interaction=False
            ).__dict__
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            return WindowTestResult(
                test_name='basic_window',
                success=False,
                error_message=str(e),
                duration_ms=duration,
                window_created=False,
                frame_displayed=False,
                user_interaction=False
            ).__dict__
    
    def _test_image_display(self) -> Dict:
        """Test image display with text overlay"""
        start_time = time.time()
        
        try:
            # Create test image with text
            test_img = np.zeros((400, 600, 3), dtype=np.uint8)
            test_img[:] = (30, 30, 30)  # Dark background
            
            # Add text overlay
            cv2.putText(test_img, 'OpenCV Display Test', (50, 200), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(test_img, 'Environment Check', (50, 250), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
            
            cv2.namedWindow('image_test', cv2.WINDOW_NORMAL)
            cv2.imshow('image_test', test_img)
            cv2.waitKey(100)
            cv2.destroyWindow('image_test')
            
            duration = (time.time() - start_time) * 1000
            
            return WindowTestResult(
                test_name='image_display',
                success=True,
                error_message=None,
                duration_ms=duration,
                window_created=True,
                frame_displayed=True,
                user_interaction=False
            ).__dict__
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            return WindowTestResult(
                test_name='image_display',
                success=False,
                error_message=str(e),
                duration_ms=duration,
                window_created=False,
                frame_displayed=False,
                user_interaction=False
            ).__dict__
    
    def _test_window_properties(self) -> Dict:
        """Test window properties and resizing"""
        start_time = time.time()
        
        try:
            cv2.namedWindow('properties_test', cv2.WINDOW_NORMAL)
            cv2.resizeWindow('properties_test', 500, 300)
            
            test_img = np.random.randint(0, 255, (300, 500, 3), dtype=np.uint8)
            cv2.imshow('properties_test', test_img)
            cv2.waitKey(100)
            cv2.destroyWindow('properties_test')
            
            duration = (time.time() - start_time) * 1000
            
            return WindowTestResult(
                test_name='window_properties',
                success=True,
                error_message=None,
                duration_ms=duration,
                window_created=True,
                frame_displayed=True,
                user_interaction=False
            ).__dict__
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            return WindowTestResult(
                test_name='window_properties',
                success=False,
                error_message=str(e),
                duration_ms=duration,
                window_created=False,
                frame_displayed=False,
                user_interaction=False
            ).__dict__
    
    def _test_multiple_windows(self) -> Dict:
        """Test multiple window creation"""
        start_time = time.time()
        
        try:
            # Create multiple windows
            windows = ['window1', 'window2', 'window3']
            
            for i, window_name in enumerate(windows):
                cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                test_img = np.zeros((200, 300, 3), dtype=np.uint8)
                test_img[:] = (i * 80, 100, 200 - i * 60)
                cv2.imshow(window_name, test_img)
            
            cv2.waitKey(100)
            
            # Destroy all windows
            for window_name in windows:
                cv2.destroyWindow(window_name)
            
            duration = (time.time() - start_time) * 1000
            
            return WindowTestResult(
                test_name='multiple_windows',
                success=True,
                error_message=None,
                duration_ms=duration,
                window_created=True,
                frame_displayed=True,
                user_interaction=False
            ).__dict__
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            return WindowTestResult(
                test_name='multiple_windows',
                success=False,
                error_message=str(e),
                duration_ms=duration,
                window_created=False,
                frame_displayed=False,
                user_interaction=False
            ).__dict__
    
    def _test_window_interaction(self) -> Dict:
        """Test window interaction (brief)"""
        start_time = time.time()
        
        try:
            cv2.namedWindow('interaction_test', cv2.WINDOW_NORMAL)
            
            test_img = np.zeros((300, 400, 3), dtype=np.uint8)
            cv2.putText(test_img, 'Interaction Test', (50, 150), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(test_img, 'Auto-closing...', (50, 200), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            
            cv2.imshow('interaction_test', test_img)
            
            # Brief wait for interaction
            key = cv2.waitKey(500)  # 500ms wait
            user_interaction = key != -1
            
            cv2.destroyWindow('interaction_test')
            
            duration = (time.time() - start_time) * 1000
            
            return WindowTestResult(
                test_name='window_interaction',
                success=True,
                error_message=None,
                duration_ms=duration,
                window_created=True,
                frame_displayed=True,
                user_interaction=user_interaction
            ).__dict__
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            return WindowTestResult(
                test_name='window_interaction',
                success=False,
                error_message=str(e),
                duration_ms=duration,
                window_created=False,
                frame_displayed=False,
                user_interaction=False
            ).__dict__
    
    def _run_camera_tests(self) -> List[Dict]:
        """Run camera display tests"""
        from .camera_manager import get_camera_manager
        
        camera_manager = get_camera_manager()
        camera_index = camera_manager.get_active_camera()
        
        if camera_index is None:
            return [{
                'test_name': 'camera_display',
                'success': False,
                'error_message': 'No camera available',
                'camera_accessible': False
            }]
        
        return [self._test_camera_display(camera_index)]
    
    def _test_camera_display(self, camera_index: int) -> Dict:
        """Test camera feed display in OpenCV window"""
        start_time = time.time()
        
        try:
            cap = cv2.VideoCapture(camera_index)
            if not cap.isOpened():
                raise Exception(f"Cannot open camera {camera_index}")
            
            cv2.namedWindow('camera_test', cv2.WINDOW_NORMAL)
            
            # Capture a few frames
            frames_captured = 0
            for _ in range(5):
                ret, frame = cap.read()
                if ret:
                    cv2.imshow('camera_test', frame)
                    cv2.waitKey(100)
                    frames_captured += 1
            
            cap.release()
            cv2.destroyWindow('camera_test')
            
            duration = (time.time() - start_time) * 1000
            
            return {
                'test_name': 'camera_display',
                'success': frames_captured > 0,
                'error_message': None if frames_captured > 0 else 'No frames captured',
                'duration_ms': duration,
                'frames_captured': frames_captured,
                'camera_accessible': True
            }
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            return {
                'test_name': 'camera_display',
                'success': False,
                'error_message': str(e),
                'duration_ms': duration,
                'frames_captured': 0,
                'camera_accessible': False
            }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Check overall window support
        window_success = any(result.success for result in self.test_results)
        
        if not window_success:
            if self.environment.display_server == 'wayland':
                recommendations.extend([
                    "🔧 Wayland detected: Try setting QT_QPA_PLATFORM=xcb",
                    "🔧 Alternative: Use X11 session instead of Wayland",
                    "🔧 For remote access: Enable X11 forwarding with 'ssh -X'"
                ])
            elif not self.environment.display_variable:
                recommendations.extend([
                    "🔧 No DISPLAY variable: Running in headless environment",
                    "🔧 Use Streamlit web interface for camera access",
                    "🔧 For remote access: Set up X11 forwarding"
                ])
            else:
                recommendations.extend([
                    "🔧 OpenCV GUI support may not be compiled",
                    "🔧 Try installing opencv-python-headless and use web interface",
                    "🔧 Check if display server is running"
                ])
        
        if not self.environment.opencv_gui_support:
            recommendations.append("🔧 OpenCV compiled without GUI support - use web interface")
        
        # Always recommend web interface as reliable alternative
        recommendations.append("✅ Recommended: Use Streamlit web interface for reliable camera access")
        
        return recommendations
    
    def _determine_overall_status(self) -> str:
        """Determine overall display status"""
        if not self.test_results:
            return "unknown"
        
        success_count = sum(1 for result in self.test_results if result.success)
        total_tests = len(self.test_results)
        
        if success_count == total_tests:
            return "excellent"
        elif success_count >= total_tests * 0.7:
            return "good"
        elif success_count > 0:
            return "limited"
        else:
            return "failed"

def run_display_diagnostics() -> Dict:
    """Run comprehensive display diagnostics"""
    diagnostics = DisplayDiagnostics()
    return diagnostics.run_comprehensive_diagnostics()
