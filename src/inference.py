"""
Real-time Inference Engine for ASL Sign Language Recognition
Processes live webcam feed for gesture recognition with MediaPipe and TensorFlow
"""

import cv2
import mediapipe as mp
import numpy as np
import tensorflow as tf
import time
import pickle
import os
from typing import Tuple, Optional, Dict, List
from collections import deque
import json

try:
    from .model import ASLModel
except ImportError:
    from model import ASLModel


class ASLInferenceEngine:
    """Real-time ASL gesture recognition engine"""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.8):
        """
        Initialize the inference engine
        
        Args:
            model_path: Path to the trained model
            confidence_threshold: Minimum confidence for predictions
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        
        # Load model and label encoder
        self.model = None
        self.label_encoder = None
        self._load_model_and_encoder()
        
        # Initialize MediaPipe
        self.mp_hands = mp.solutions.hands
        self.mp_drawing = mp.solutions.drawing_utils
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        # Performance tracking
        self.fps_counter = deque(maxlen=30)
        self.prediction_history = deque(maxlen=10)
        
        # Statistics
        self.frame_count = 0
        self.prediction_count = 0
        self.start_time = time.time()

        # Enhanced tracking for visual feedback
        self.prediction_history = []  # Store recent predictions
        self.last_stable_prediction = None
        self.stable_prediction_count = 0
        self.prediction_stability_threshold = 5  # Frames needed for stable prediction
    
    def _load_model_and_encoder(self):
        """Load the trained model and label encoder"""
        try:
            # Load model
            self.model = tf.keras.models.load_model(self.model_path)
            print(f"Model loaded from {self.model_path}")
            
            # Load label encoder
            model_dir = os.path.dirname(self.model_path)
            encoder_path = os.path.join("data/processed", "label_encoder.pkl")
            
            if os.path.exists(encoder_path):
                with open(encoder_path, 'rb') as f:
                    self.label_encoder = pickle.load(f)
                print(f"Label encoder loaded from {encoder_path}")
            else:
                print("Warning: Label encoder not found. Using numeric predictions.")
                
        except Exception as e:
            raise RuntimeError(f"Failed to load model or encoder: {e}")
    
    def extract_hand_region(self, image: np.ndarray, landmarks) -> Optional[np.ndarray]:
        """
        Extract and preprocess hand region from image
        
        Args:
            image: Input image
            landmarks: MediaPipe hand landmarks
            
        Returns:
            Preprocessed hand region (224, 224, 3) or None
        """
        if not landmarks:
            return None
        
        h, w, _ = image.shape
        
        # Get bounding box coordinates
        x_coords = [landmark.x * w for landmark in landmarks.landmark]
        y_coords = [landmark.y * h for landmark in landmarks.landmark]
        
        x_min, x_max = int(min(x_coords)), int(max(x_coords))
        y_min, y_max = int(min(y_coords)), int(max(y_coords))
        
        # Add padding
        padding = 30
        x_min = max(0, x_min - padding)
        y_min = max(0, y_min - padding)
        x_max = min(w, x_max + padding)
        y_max = min(h, y_max + padding)
        
        # Extract hand region
        hand_region = image[y_min:y_max, x_min:x_max]
        
        if hand_region.size > 0:
            # Resize to model input size
            hand_region = cv2.resize(hand_region, (224, 224))
            
            # Normalize pixel values
            hand_region = hand_region.astype(np.float32) / 255.0
            
            return hand_region
        
        return None
    
    def predict_gesture(self, hand_region: np.ndarray) -> Tuple[str, float]:
        """
        Predict ASL gesture from hand region
        
        Args:
            hand_region: Preprocessed hand region (224, 224, 3)
            
        Returns:
            Tuple of (predicted_letter, confidence)
        """
        # Add batch dimension
        input_batch = np.expand_dims(hand_region, axis=0)
        
        # Make prediction
        predictions = self.model.predict(input_batch, verbose=0)
        predicted_class = np.argmax(predictions[0])
        confidence = np.max(predictions[0])
        
        # Convert to letter
        if self.label_encoder is not None:
            predicted_letter = self.label_encoder.inverse_transform([predicted_class])[0]
        else:
            predicted_letter = str(predicted_class)
        
        return predicted_letter, confidence
    
    def smooth_predictions(self, prediction: str, confidence: float) -> Tuple[str, float]:
        """
        Smooth predictions using history to reduce noise
        
        Args:
            prediction: Current prediction
            confidence: Current confidence
            
        Returns:
            Smoothed prediction and confidence
        """
        # Add to history if confidence is above threshold
        if confidence >= self.confidence_threshold:
            self.prediction_history.append((prediction, confidence))
        
        if len(self.prediction_history) == 0:
            return prediction, confidence
        
        # Get most common prediction in recent history
        recent_predictions = [pred for pred, conf in self.prediction_history[-5:]]
        
        if len(recent_predictions) > 0:
            # Find most frequent prediction
            prediction_counts = {}
            for pred in recent_predictions:
                prediction_counts[pred] = prediction_counts.get(pred, 0) + 1
            
            most_common = max(prediction_counts, key=prediction_counts.get)
            
            # Calculate average confidence for most common prediction
            confidences = [conf for pred, conf in self.prediction_history[-5:] if pred == most_common]
            avg_confidence = np.mean(confidences) if confidences else confidence
            
            return most_common, avg_confidence
        
        return prediction, confidence
    
    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        Process a single frame for gesture recognition
        
        Args:
            frame: Input frame from webcam
            
        Returns:
            Tuple of (processed_frame, prediction_info)
        """
        frame_start_time = time.time()
        
        # Flip frame horizontally for mirror effect
        frame = cv2.flip(frame, 1)
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Process with MediaPipe
        results = self.hands.process(rgb_frame)
        
        prediction_info = {
            'letter': None,
            'confidence': 0.0,
            'hand_detected': False,
            'fps': 0.0
        }
        
        # Process hand landmarks
        if results.multi_hand_landmarks:
            prediction_info['hand_detected'] = True
            
            for hand_landmarks in results.multi_hand_landmarks:
                # Draw landmarks
                self.mp_drawing.draw_landmarks(
                    frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS
                )
                
                # Extract hand region
                hand_region = self.extract_hand_region(frame, hand_landmarks)
                
                if hand_region is not None:
                    # Make prediction
                    letter, confidence = self.predict_gesture(hand_region)
                    
                    # Apply smoothing
                    letter, confidence = self.smooth_predictions(letter, confidence)
                    
                    prediction_info['letter'] = letter
                    prediction_info['confidence'] = confidence
                    
                    # Show hand region in corner
                    display_hand = (hand_region * 255).astype(np.uint8)
                    display_hand = cv2.resize(display_hand, (150, 150))
                    frame[10:160, 10:160] = display_hand
                    
                    self.prediction_count += 1

        # Enhanced prediction stability tracking
        current_prediction = prediction_info.get('letter', '')
        current_confidence = prediction_info.get('confidence', 0.0)

        # Add to prediction history
        self.prediction_history.append({
            'letter': current_prediction,
            'confidence': current_confidence,
            'timestamp': time.time()
        })

        # Keep only recent predictions (last 10 frames)
        if len(self.prediction_history) > 10:
            self.prediction_history.pop(0)

        # Check for stable prediction
        if current_prediction and current_confidence >= self.confidence_threshold:
            if current_prediction == self.last_stable_prediction:
                self.stable_prediction_count += 1
            else:
                self.last_stable_prediction = current_prediction
                self.stable_prediction_count = 1
        else:
            self.stable_prediction_count = 0

        # Add stability info to prediction_info
        prediction_info['stable_prediction'] = self.last_stable_prediction if self.stable_prediction_count >= self.prediction_stability_threshold else None
        prediction_info['stability_count'] = self.stable_prediction_count
        prediction_info['prediction_history'] = self.prediction_history[-5:]  # Last 5 predictions

        # Calculate FPS
        frame_time = time.time() - frame_start_time
        fps = 1.0 / frame_time if frame_time > 0 else 0
        self.fps_counter.append(fps)
        prediction_info['fps'] = np.mean(self.fps_counter)

        self.frame_count += 1
        
        return frame, prediction_info
    
    def add_overlay_info(self, frame: np.ndarray, prediction_info: Dict) -> np.ndarray:
        """
        Add enhanced prediction and performance info overlay to frame with prominent visual feedback

        Args:
            frame: Input frame
            prediction_info: Prediction information dictionary

        Returns:
            Frame with comprehensive overlay information
        """
        h, w = frame.shape[:2]
        overlay = frame.copy()

        # Enhanced prediction display with stability tracking
        letter = prediction_info.get('letter', '')
        confidence = prediction_info.get('confidence', 0.0)
        stable_prediction = prediction_info.get('stable_prediction', None)
        stability_count = prediction_info.get('stability_count', 0)
        prediction_history = prediction_info.get('prediction_history', [])

        if prediction_info.get('hand_detected', False):

            if letter and confidence >= self.confidence_threshold:
                # HIGH CONFIDENCE PREDICTION - Large, prominent display

                # Main prediction box (top-right, larger)
                box_width = 220
                box_height = 180
                box_x = w - box_width - 20
                box_y = 20

                # Background color based on stability
                if stable_prediction:
                    # Stable prediction - bright green
                    bg_color = (0, 220, 0)
                    border_color = (0, 255, 0)
                    border_thickness = 4
                else:
                    # Unstable prediction - orange
                    bg_color = (0, 180, 255)
                    border_color = (0, 255, 255)
                    border_thickness = 2

                cv2.rectangle(overlay, (box_x, box_y), (box_x + box_width, box_y + box_height),
                             bg_color, -1)
                cv2.rectangle(overlay, (box_x, box_y), (box_x + box_width, box_y + box_height),
                             border_color, border_thickness)

                # Large letter display
                letter_size = 3.5 if stable_prediction else 2.8
                letter_thickness = 10 if stable_prediction else 7
                text_size = cv2.getTextSize(letter.upper(), cv2.FONT_HERSHEY_SIMPLEX, letter_size, letter_thickness)[0]
                letter_x = box_x + (box_width - text_size[0]) // 2
                letter_y = box_y + 90

                # Letter with shadow effect
                cv2.putText(overlay, letter.upper(), (letter_x + 3, letter_y + 3),
                           cv2.FONT_HERSHEY_SIMPLEX, letter_size, (0, 0, 0), letter_thickness)
                cv2.putText(overlay, letter.upper(), (letter_x, letter_y),
                           cv2.FONT_HERSHEY_SIMPLEX, letter_size, (255, 255, 255), letter_thickness)

                # Confidence and stability display
                conf_text = f"{confidence:.1%}"
                cv2.putText(overlay, conf_text, (box_x + 10, box_y + 130),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

                # Stability indicator
                if stable_prediction:
                    stability_text = f"STABLE ✓"
                    cv2.putText(overlay, stability_text, (box_x + 10, box_y + 155),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                else:
                    stability_text = f"Stabilizing... {stability_count}/{self.prediction_stability_threshold}"
                    cv2.putText(overlay, stability_text, (box_x + 10, box_y + 155),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                # Center screen confirmation for stable high confidence predictions
                if stable_prediction and confidence > 0.85:
                    center_text = f"✓ {letter.upper()}"
                    center_size = cv2.getTextSize(center_text, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 8)[0]
                    center_x = (w - center_size[0]) // 2
                    center_y = h // 2

                    # Pulsing effect based on frame count
                    pulse = int(abs(np.sin(self.frame_count * 0.3)) * 50)

                    # Semi-transparent background with pulse
                    cv2.rectangle(overlay, (center_x - 30, center_y - 50),
                                 (center_x + center_size[0] + 30, center_y + 30),
                                 (0, 255 - pulse, 0), -1)
                    cv2.putText(overlay, center_text, (center_x, center_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 2.5, (0, 0, 0), 8)

            elif letter:
                # LOW CONFIDENCE PREDICTION - Smaller, yellow display

                box_width = 180
                box_height = 120
                box_x = w - box_width - 20
                box_y = 20

                # Background with low confidence color (yellow/orange)
                cv2.rectangle(overlay, (box_x, box_y), (box_x + box_width, box_y + box_height),
                             (0, 165, 255), -1)
                cv2.rectangle(overlay, (box_x, box_y), (box_x + box_width, box_y + box_height),
                             (0, 255, 255), 2)

                # Medium letter display
                letter_size = 2.0
                letter_thickness = 5
                text_size = cv2.getTextSize(letter.upper(), cv2.FONT_HERSHEY_SIMPLEX, letter_size, letter_thickness)[0]
                letter_x = box_x + (box_width - text_size[0]) // 2
                letter_y = box_y + 70

                cv2.putText(overlay, letter.upper(), (letter_x, letter_y),
                           cv2.FONT_HERSHEY_SIMPLEX, letter_size, (0, 0, 0), letter_thickness)

                # Confidence with "?" indicator
                conf_text = f"{confidence:.1%} ?"
                cv2.putText(overlay, conf_text, (box_x + 10, box_y + 105),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

            else:
                # HAND DETECTED BUT NO PREDICTION - Blue display

                box_width = 160
                box_height = 80
                box_x = w - box_width - 20
                box_y = 20

                cv2.rectangle(overlay, (box_x, box_y), (box_x + box_width, box_y + box_height),
                             (200, 100, 0), -1)
                cv2.rectangle(overlay, (box_x, box_y), (box_x + box_width, box_y + box_height),
                             (255, 150, 0), 2)

                cv2.putText(overlay, "DETECTING...", (box_x + 10, box_y + 50),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        else:
            # NO HAND DETECTED - Gray display

            box_width = 160
            box_height = 80
            box_x = w - box_width - 20
            box_y = 20

            cv2.rectangle(overlay, (box_x, box_y), (box_x + box_width, box_y + box_height),
                         (100, 100, 100), -1)
            cv2.rectangle(overlay, (box_x, box_y), (box_x + box_width, box_y + box_height),
                         (150, 150, 150), 2)

            cv2.putText(overlay, "NO HAND", (box_x + 20, box_y + 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # Enhanced performance and statistics display (bottom-left)
        fps = prediction_info.get('fps', 0)
        stats_width = 280
        stats_height = 140
        stats_x = 10
        stats_y = h - stats_height - 10

        # Semi-transparent background for stats
        cv2.rectangle(overlay, (stats_x, stats_y), (stats_x + stats_width, stats_y + stats_height),
                     (40, 40, 40), -1)
        cv2.rectangle(overlay, (stats_x, stats_y), (stats_x + stats_width, stats_y + stats_height),
                     (100, 100, 100), 2)

        # Performance statistics
        cv2.putText(overlay, "PERFORMANCE", (stats_x + 10, stats_y + 25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(overlay, f"FPS: {fps:.1f}", (stats_x + 10, stats_y + 50),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        cv2.putText(overlay, f"Frames: {self.frame_count}", (stats_x + 10, stats_y + 75),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        cv2.putText(overlay, f"Predictions: {self.prediction_count}", (stats_x + 10, stats_y + 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

        # Prediction rate
        if self.frame_count > 0:
            pred_rate = (self.prediction_count / self.frame_count) * 100
            cv2.putText(overlay, f"Rate: {pred_rate:.1f}%", (stats_x + 10, stats_y + 125),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

        # Instructions display (top-left)
        instructions = [
            "CONTROLS:",
            "Q - Quit",
            "R - Reset Stats",
            "S - Screenshot"
        ]

        inst_width = 150
        inst_height = 120
        inst_x = 10
        inst_y = 10

        cv2.rectangle(overlay, (inst_x, inst_y), (inst_x + inst_width, inst_y + inst_height),
                     (60, 60, 60), -1)
        cv2.rectangle(overlay, (inst_x, inst_y), (inst_x + inst_width, inst_y + inst_height),
                     (120, 120, 120), 1)

        for i, instruction in enumerate(instructions):
            y_pos = inst_y + 25 + (i * 20)
            color = (255, 255, 0) if i == 0 else (200, 200, 200)
            font_size = 0.5 if i == 0 else 0.4
            cv2.putText(overlay, instruction, (inst_x + 5, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, font_size, color, 1)

        # Model information display (top-center)
        model_info = f"Model: ASL Recognition | Classes: 36 | Threshold: {self.confidence_threshold:.2f}"
        model_size = cv2.getTextSize(model_info, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
        model_x = (w - model_size[0]) // 2
        model_y = 25

        # Background for model info
        cv2.rectangle(overlay, (model_x - 10, model_y - 20), (model_x + model_size[0] + 10, model_y + 10),
                     (0, 0, 0), -1)
        cv2.putText(overlay, model_info, (model_x, model_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Hand detection indicator (bottom-right)
        if prediction_info.get('hand_detected', False):
            hand_indicator = "HAND DETECTED"
            hand_color = (0, 255, 0)
        else:
            hand_indicator = "NO HAND"
            hand_color = (0, 0, 255)

        hand_size = cv2.getTextSize(hand_indicator, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        hand_x = w - hand_size[0] - 20
        hand_y = h - 30

        cv2.putText(overlay, hand_indicator, (hand_x, hand_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, hand_color, 2)

        # Prediction history display (right side, below main prediction)
        if prediction_history:
            history_width = 200
            history_height = 150
            history_x = w - history_width - 20
            history_y = 220

            cv2.rectangle(overlay, (history_x, history_y), (history_x + history_width, history_y + history_height),
                         (30, 30, 30), -1)
            cv2.rectangle(overlay, (history_x, history_y), (history_x + history_width, history_y + history_height),
                         (100, 100, 100), 1)

            cv2.putText(overlay, "RECENT PREDICTIONS", (history_x + 5, history_y + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

            # Show last 5 predictions
            for i, pred in enumerate(prediction_history[-5:]):
                y_pos = history_y + 40 + (i * 20)
                pred_letter = pred.get('letter', '?')
                pred_conf = pred.get('confidence', 0.0)

                if pred_letter and pred_conf >= self.confidence_threshold:
                    color = (0, 255, 0) if pred_conf > 0.8 else (0, 255, 255)
                    text = f"{pred_letter.upper()}: {pred_conf:.1%}"
                else:
                    color = (100, 100, 100)
                    text = "---"

                cv2.putText(overlay, text, (history_x + 10, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # ASL alphabet reference (bottom-center) - show current target area
        alphabet_text = "ASL: 0-9, A-Z (36 classes)"
        alphabet_size = cv2.getTextSize(alphabet_text, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
        alphabet_x = (w - alphabet_size[0]) // 2
        alphabet_y = h - 10

        cv2.putText(overlay, alphabet_text, (alphabet_x, alphabet_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)

        # Gesture guidance (center-left)
        if not prediction_info.get('hand_detected', False):
            guidance_text = [
                "SHOW YOUR HAND",
                "• Position hand in center",
                "• Clear background",
                "• Good lighting",
                "• Hold gesture steady"
            ]

            guidance_x = w // 2 - 100
            guidance_y = h // 2 - 60

            for i, guide in enumerate(guidance_text):
                y_pos = guidance_y + (i * 25)
                color = (0, 255, 255) if i == 0 else (200, 200, 200)
                font_size = 0.7 if i == 0 else 0.5
                thickness = 2 if i == 0 else 1

                cv2.putText(overlay, guide, (guidance_x, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, font_size, color, thickness)

        # Blend overlay with original frame for semi-transparency
        alpha = 0.85
        frame = cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0)

        return frame

    def test_display_capability(self) -> bool:
        """Test if OpenCV window display is available"""
        try:
            # Quick test of window creation
            test_img = np.zeros((100, 100, 3), dtype=np.uint8)
            cv2.namedWindow('display_test', cv2.WINDOW_NORMAL)
            cv2.imshow('display_test', test_img)
            cv2.waitKey(1)
            cv2.destroyWindow('display_test')
            return True
        except Exception:
            return False

    def run_inference_with_display_check(self, camera_index: int = None, window_name: str = "ASL Recognition"):
        """
        Run inference with display capability check and fallback guidance

        Args:
            camera_index: Camera device index (None for auto-detection)
            window_name: Name of the display window
        """
        # Test display capability first
        if not self.test_display_capability():
            print("⚠️  OpenCV window display not available in this environment")
            print("🔍 Detected issues:")

            import os
            display_var = os.environ.get('DISPLAY')
            session_type = os.environ.get('XDG_SESSION_TYPE')
            wayland_display = os.environ.get('WAYLAND_DISPLAY')

            if not display_var:
                print("   • No DISPLAY variable (headless/remote environment)")
            if session_type == 'wayland':
                print("   • Wayland session detected (limited OpenCV support)")
            if wayland_display:
                print("   • Wayland display server active")

            print("\n💡 Recommended solutions:")
            print("   1. Use Streamlit web interface: http://localhost:8507")
            print("   2. For remote access: ssh -X username@hostname")
            print("   3. For Wayland: export QT_QPA_PLATFORM=xcb")
            print("   4. Switch to X11 session")

            print("\n🌐 Starting web interface guidance...")
            print("   Run: streamlit run app.py --server.port 8507")
            return

        # If display is available, run normal inference
        print("✅ OpenCV display available - starting inference...")
        return self.run_inference(camera_index, window_name)

    def run_inference(self, camera_index: int = None, window_name: str = "ASL Recognition"):
        """
        Run real-time inference on webcam feed with enhanced window display

        Args:
            camera_index: Camera device index (None to auto-detect)
            window_name: OpenCV window name
        """
        # Auto-detect working camera if not specified using camera manager
        if camera_index is None:
            from .camera_manager import get_camera_manager
            camera_manager = get_camera_manager()
            camera_index = camera_manager.get_active_camera()
            if camera_index is None:
                raise RuntimeError("No working camera found")
            print(f"Using persistent camera index: {camera_index}")

        # Initialize camera with enhanced settings
        cap = cv2.VideoCapture(camera_index)

        if not cap.isOpened():
            raise RuntimeError(f"Could not open camera {camera_index}")

        # Set camera properties for better performance
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)

        # Configure OpenCV window for better display
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 800, 600)

        print(f"Camera opened successfully at index {camera_index}")
        print(f"Resolution: {int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}")
        print(f"FPS: {cap.get(cv2.CAP_PROP_FPS)}")
        
        # Set camera properties for better performance
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        print("Starting real-time ASL recognition...")
        print("Press 'q' to quit, 'r' to reset statistics")
        print(f"Window '{window_name}' should appear shortly...")

        # Initialize window display
        try:
            # Create initial test frame to ensure window appears
            test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.putText(test_frame, 'Initializing Camera...', (150, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.imshow(window_name, test_frame)
            cv2.waitKey(100)  # Brief pause to ensure window creation

            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("Failed to read frame from camera")
                    break

                frame_count += 1

                # Process frame
                processed_frame, prediction_info = self.process_frame(frame)

                # Add overlay information
                display_frame = self.add_overlay_info(processed_frame, prediction_info)

                # Add frame counter for debugging
                cv2.putText(display_frame, f"Frame: {frame_count}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                # Show frame with error handling
                try:
                    cv2.imshow(window_name, display_frame)
                except Exception as e:
                    print(f"Window display error: {e}")
                    break

                # Handle key presses with enhanced functionality
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("Quit key pressed")
                    break
                elif key == ord('r'):
                    self._reset_statistics()
                    print("Statistics reset")
                elif key == ord('s'):
                    # Save screenshot with prediction info
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    current_prediction = processed_frame
                    letter = prediction_info.get('letter', 'unknown')
                    confidence = prediction_info.get('confidence', 0.0)

                    filename = f"asl_screenshot_{timestamp}_{letter}_{confidence:.2f}.jpg"
                    cv2.imwrite(filename, current_prediction)
                    print(f"Screenshot saved: {filename}")
                elif key == ord('h'):
                    # Show help
                    print("\n=== ASL Recognition Controls ===")
                    print("Q - Quit application")
                    print("R - Reset statistics")
                    print("S - Save screenshot")
                    print("H - Show this help")
                    print("================================\n")

                # Debug: Print status every 30 frames
                if frame_count % 30 == 0:
                    print(f"Processing frame {frame_count}, window active")

        except Exception as e:
            print(f"Inference error: {e}")
        finally:
            print("Cleaning up...")
            cap.release()
            cv2.destroyAllWindows()
            print("Camera and windows closed")

    def run_inference_with_fallback(self, camera_index: int = None, window_name: str = "ASL Recognition"):
        """
        Run real-time inference with display fallback options for Wayland/X11 compatibility

        Args:
            camera_index: Camera device index (None to auto-detect)
            window_name: OpenCV window name
        """
        import os

        # Try different display methods for better compatibility
        display_methods = [
            ("X11 with Qt", {"QT_QPA_PLATFORM": "xcb"}),
            ("Wayland with Qt", {"QT_QPA_PLATFORM": "wayland"}),
            ("Default", {})
        ]

        for method_name, env_vars in display_methods:
            print(f"Trying display method: {method_name}")

            # Set environment variables
            original_env = {}
            for key, value in env_vars.items():
                original_env[key] = os.environ.get(key)
                os.environ[key] = value

            try:
                self.run_inference(camera_index, window_name)
                return  # Success, exit
            except Exception as e:
                print(f"Display method '{method_name}' failed: {e}")

                # Restore original environment
                for key, original_value in original_env.items():
                    if original_value is None:
                        os.environ.pop(key, None)
                    else:
                        os.environ[key] = original_value

        print("All display methods failed. Camera hardware may not be accessible.")

    def run_inference_headless(self, camera_index: int = None, duration: int = 30):
        """
        Run inference without display window (headless mode) for testing

        Args:
            camera_index: Camera device index (None to auto-detect)
            duration: Duration in seconds to run inference
        """
        # Auto-detect working camera if not specified using camera manager
        if camera_index is None:
            from .camera_manager import get_camera_manager
            camera_manager = get_camera_manager()
            camera_index = camera_manager.get_active_camera()
            if camera_index is None:
                raise RuntimeError("No working camera found")
            print(f"Using persistent camera index: {camera_index}")

        cap = cv2.VideoCapture(camera_index)

        if not cap.isOpened():
            raise RuntimeError(f"Could not open camera {camera_index}")

        print(f"Running headless inference for {duration} seconds...")
        print("Camera feed active, processing frames without display...")

        import time
        start_time = time.time()
        frame_count = 0

        try:
            while time.time() - start_time < duration:
                ret, frame = cap.read()
                if not ret:
                    print("Failed to read frame from camera")
                    break

                frame_count += 1

                # Process frame
                processed_frame, prediction_info = self.process_frame(frame)

                # Print predictions every 30 frames
                if frame_count % 30 == 0:
                    if prediction_info and 'prediction' in prediction_info:
                        pred = prediction_info['prediction']
                        conf = prediction_info.get('confidence', 0)
                        print(f"Frame {frame_count}: Predicted '{pred}' (confidence: {conf:.2f})")
                    else:
                        print(f"Frame {frame_count}: Processing...")

        finally:
            cap.release()
            print(f"Headless inference completed. Processed {frame_count} frames.")
            self._print_session_summary()
    
    def _reset_statistics(self):
        """Reset performance statistics"""
        self.frame_count = 0
        self.prediction_count = 0
        self.start_time = time.time()
        self.fps_counter.clear()
        self.prediction_history.clear()
    
    def _print_session_summary(self):
        """Print session performance summary"""
        total_time = time.time() - self.start_time
        avg_fps = self.frame_count / total_time if total_time > 0 else 0
        
        print("\n" + "="*50)
        print("SESSION SUMMARY")
        print("="*50)
        print(f"Total frames processed: {self.frame_count}")
        print(f"Total predictions made: {self.prediction_count}")
        print(f"Session duration: {total_time:.2f} seconds")
        print(f"Average FPS: {avg_fps:.2f}")
        print(f"Prediction rate: {self.prediction_count/total_time:.2f} predictions/sec")
        print("="*50)


if __name__ == "__main__":
    # Example usage
    model_path = "data/models/best_model.h5"
    
    if os.path.exists(model_path):
        engine = ASLInferenceEngine(model_path, confidence_threshold=0.8)
        engine.run_inference()
    else:
        print(f"Model not found at {model_path}")
        print("Please train a model first using the training pipeline.")
