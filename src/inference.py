"""
Real-time Inference Engine for ASL Sign Language Recognition
Processes live webcam feed for gesture recognition with MediaPipe and TensorFlow
"""

import cv2
import mediapipe as mp
import numpy as np
import tensorflow as tf
import time
import pickle
import os
from typing import Tuple, Optional, Dict, List
from collections import deque
import json

try:
    from .model import ASLModel
except ImportError:
    from model import ASLModel


class ASLInferenceEngine:
    """Real-time ASL gesture recognition engine"""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.8):
        """
        Initialize the inference engine
        
        Args:
            model_path: Path to the trained model
            confidence_threshold: Minimum confidence for predictions
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        
        # Load model and label encoder
        self.model = None
        self.label_encoder = None
        self._load_model_and_encoder()
        
        # Initialize MediaPipe
        self.mp_hands = mp.solutions.hands
        self.mp_drawing = mp.solutions.drawing_utils
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        # Performance tracking
        self.fps_counter = deque(maxlen=30)
        self.prediction_history = deque(maxlen=10)
        
        # Statistics
        self.frame_count = 0
        self.prediction_count = 0
        self.start_time = time.time()
    
    def _load_model_and_encoder(self):
        """Load the trained model and label encoder"""
        try:
            # Load model
            self.model = tf.keras.models.load_model(self.model_path)
            print(f"Model loaded from {self.model_path}")
            
            # Load label encoder
            model_dir = os.path.dirname(self.model_path)
            encoder_path = os.path.join("data/processed", "label_encoder.pkl")
            
            if os.path.exists(encoder_path):
                with open(encoder_path, 'rb') as f:
                    self.label_encoder = pickle.load(f)
                print(f"Label encoder loaded from {encoder_path}")
            else:
                print("Warning: Label encoder not found. Using numeric predictions.")
                
        except Exception as e:
            raise RuntimeError(f"Failed to load model or encoder: {e}")
    
    def extract_hand_region(self, image: np.ndarray, landmarks) -> Optional[np.ndarray]:
        """
        Extract and preprocess hand region from image
        
        Args:
            image: Input image
            landmarks: MediaPipe hand landmarks
            
        Returns:
            Preprocessed hand region (224, 224, 3) or None
        """
        if not landmarks:
            return None
        
        h, w, _ = image.shape
        
        # Get bounding box coordinates
        x_coords = [landmark.x * w for landmark in landmarks.landmark]
        y_coords = [landmark.y * h for landmark in landmarks.landmark]
        
        x_min, x_max = int(min(x_coords)), int(max(x_coords))
        y_min, y_max = int(min(y_coords)), int(max(y_coords))
        
        # Add padding
        padding = 30
        x_min = max(0, x_min - padding)
        y_min = max(0, y_min - padding)
        x_max = min(w, x_max + padding)
        y_max = min(h, y_max + padding)
        
        # Extract hand region
        hand_region = image[y_min:y_max, x_min:x_max]
        
        if hand_region.size > 0:
            # Resize to model input size
            hand_region = cv2.resize(hand_region, (224, 224))
            
            # Normalize pixel values
            hand_region = hand_region.astype(np.float32) / 255.0
            
            return hand_region
        
        return None
    
    def predict_gesture(self, hand_region: np.ndarray) -> Tuple[str, float]:
        """
        Predict ASL gesture from hand region
        
        Args:
            hand_region: Preprocessed hand region (224, 224, 3)
            
        Returns:
            Tuple of (predicted_letter, confidence)
        """
        # Add batch dimension
        input_batch = np.expand_dims(hand_region, axis=0)
        
        # Make prediction
        predictions = self.model.predict(input_batch, verbose=0)
        predicted_class = np.argmax(predictions[0])
        confidence = np.max(predictions[0])
        
        # Convert to letter
        if self.label_encoder is not None:
            predicted_letter = self.label_encoder.inverse_transform([predicted_class])[0]
        else:
            predicted_letter = str(predicted_class)
        
        return predicted_letter, confidence
    
    def smooth_predictions(self, prediction: str, confidence: float) -> Tuple[str, float]:
        """
        Smooth predictions using history to reduce noise
        
        Args:
            prediction: Current prediction
            confidence: Current confidence
            
        Returns:
            Smoothed prediction and confidence
        """
        # Add to history if confidence is above threshold
        if confidence >= self.confidence_threshold:
            self.prediction_history.append((prediction, confidence))
        
        if len(self.prediction_history) == 0:
            return prediction, confidence
        
        # Get most common prediction in recent history
        recent_predictions = [pred for pred, conf in self.prediction_history[-5:]]
        
        if len(recent_predictions) > 0:
            # Find most frequent prediction
            prediction_counts = {}
            for pred in recent_predictions:
                prediction_counts[pred] = prediction_counts.get(pred, 0) + 1
            
            most_common = max(prediction_counts, key=prediction_counts.get)
            
            # Calculate average confidence for most common prediction
            confidences = [conf for pred, conf in self.prediction_history[-5:] if pred == most_common]
            avg_confidence = np.mean(confidences) if confidences else confidence
            
            return most_common, avg_confidence
        
        return prediction, confidence
    
    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        Process a single frame for gesture recognition
        
        Args:
            frame: Input frame from webcam
            
        Returns:
            Tuple of (processed_frame, prediction_info)
        """
        frame_start_time = time.time()
        
        # Flip frame horizontally for mirror effect
        frame = cv2.flip(frame, 1)
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Process with MediaPipe
        results = self.hands.process(rgb_frame)
        
        prediction_info = {
            'letter': None,
            'confidence': 0.0,
            'hand_detected': False,
            'fps': 0.0
        }
        
        # Process hand landmarks
        if results.multi_hand_landmarks:
            prediction_info['hand_detected'] = True
            
            for hand_landmarks in results.multi_hand_landmarks:
                # Draw landmarks
                self.mp_drawing.draw_landmarks(
                    frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS
                )
                
                # Extract hand region
                hand_region = self.extract_hand_region(frame, hand_landmarks)
                
                if hand_region is not None:
                    # Make prediction
                    letter, confidence = self.predict_gesture(hand_region)
                    
                    # Apply smoothing
                    letter, confidence = self.smooth_predictions(letter, confidence)
                    
                    prediction_info['letter'] = letter
                    prediction_info['confidence'] = confidence
                    
                    # Show hand region in corner
                    display_hand = (hand_region * 255).astype(np.uint8)
                    display_hand = cv2.resize(display_hand, (150, 150))
                    frame[10:160, 10:160] = display_hand
                    
                    self.prediction_count += 1
        
        # Calculate FPS
        frame_time = time.time() - frame_start_time
        fps = 1.0 / frame_time if frame_time > 0 else 0
        self.fps_counter.append(fps)
        prediction_info['fps'] = np.mean(self.fps_counter)
        
        self.frame_count += 1
        
        return frame, prediction_info
    
    def add_overlay_info(self, frame: np.ndarray, prediction_info: Dict) -> np.ndarray:
        """
        Add prediction and performance info overlay to frame
        
        Args:
            frame: Input frame
            prediction_info: Prediction information dictionary
            
        Returns:
            Frame with overlay information
        """
        h, w = frame.shape[:2]
        
        # Create semi-transparent overlay
        overlay = frame.copy()
        
        # Add prediction info
        if prediction_info['hand_detected']:
            if prediction_info['letter'] and prediction_info['confidence'] >= self.confidence_threshold:
                # High confidence prediction
                letter = prediction_info['letter']
                confidence = prediction_info['confidence']
                
                # Main prediction display
                cv2.rectangle(overlay, (w-300, 10), (w-10, 120), (0, 255, 0), -1)
                cv2.putText(overlay, f"Letter: {letter}", (w-290, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 3)
                cv2.putText(overlay, f"Conf: {confidence:.2f}", (w-290, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            else:
                # Low confidence or no prediction
                cv2.rectangle(overlay, (w-300, 10), (w-10, 80), (0, 165, 255), -1)
                cv2.putText(overlay, "Low Confidence", (w-290, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        else:
            # No hand detected
            cv2.rectangle(overlay, (w-300, 10), (w-10, 80), (0, 0, 255), -1)
            cv2.putText(overlay, "No Hand Detected", (w-290, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # Add performance info
        fps = prediction_info['fps']
        cv2.rectangle(overlay, (10, h-100), (250, h-10), (50, 50, 50), -1)
        cv2.putText(overlay, f"FPS: {fps:.1f}", (20, h-70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(overlay, f"Frames: {self.frame_count}", (20, h-45), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(overlay, f"Predictions: {self.prediction_count}", (20, h-20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Blend overlay with original frame
        alpha = 0.7
        frame = cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0)
        
        return frame
    
    def run_inference(self, camera_index: int = None, window_name: str = "ASL Recognition"):
        """
        Run real-time inference on webcam feed

        Args:
            camera_index: Camera device index (None to auto-detect)
            window_name: OpenCV window name
        """
        # Auto-detect working camera if not specified
        if camera_index is None:
            from .utils import find_working_camera
            camera_index = find_working_camera()
            if camera_index == -1:
                raise RuntimeError("No working camera found")
            print(f"Auto-detected camera index: {camera_index}")

        cap = cv2.VideoCapture(camera_index)

        if not cap.isOpened():
            raise RuntimeError(f"Could not open camera {camera_index}")
        
        # Set camera properties for better performance
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        print("Starting real-time ASL recognition...")
        print("Press 'q' to quit, 'r' to reset statistics")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("Failed to read frame from camera")
                    break
                
                # Process frame
                processed_frame, prediction_info = self.process_frame(frame)
                
                # Add overlay information
                display_frame = self.add_overlay_info(processed_frame, prediction_info)
                
                # Show frame
                cv2.imshow(window_name, display_frame)
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('r'):
                    self._reset_statistics()
                    print("Statistics reset")
        
        finally:
            cap.release()
            cv2.destroyAllWindows()
            self._print_session_summary()
    
    def _reset_statistics(self):
        """Reset performance statistics"""
        self.frame_count = 0
        self.prediction_count = 0
        self.start_time = time.time()
        self.fps_counter.clear()
        self.prediction_history.clear()
    
    def _print_session_summary(self):
        """Print session performance summary"""
        total_time = time.time() - self.start_time
        avg_fps = self.frame_count / total_time if total_time > 0 else 0
        
        print("\n" + "="*50)
        print("SESSION SUMMARY")
        print("="*50)
        print(f"Total frames processed: {self.frame_count}")
        print(f"Total predictions made: {self.prediction_count}")
        print(f"Session duration: {total_time:.2f} seconds")
        print(f"Average FPS: {avg_fps:.2f}")
        print(f"Prediction rate: {self.prediction_count/total_time:.2f} predictions/sec")
        print("="*50)


if __name__ == "__main__":
    # Example usage
    model_path = "data/models/best_model.h5"
    
    if os.path.exists(model_path):
        engine = ASLInferenceEngine(model_path, confidence_threshold=0.8)
        engine.run_inference()
    else:
        print(f"Model not found at {model_path}")
        print("Please train a model first using the training pipeline.")
