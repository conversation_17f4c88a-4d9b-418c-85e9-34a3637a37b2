"""
CNN Model Architecture for ASL Sign Language Recognition
Implements optimized CNN architecture for hand gesture classification
"""

import tensorflow as tf
from tensorflow.keras import layers, models, optimizers, callbacks
from tensorflow.keras.applications import MobileNetV2, EfficientNetB0
import numpy as np
from typing import Tuple, Optional


class ASLModel:
    """CNN model for ASL gesture recognition"""
    
    def __init__(self, input_shape: Tuple[int, int, int] = (224, 224, 3), 
                 num_classes: int = 26):
        """
        Initialize the ASL model
        
        Args:
            input_shape: Input image shape (height, width, channels)
            num_classes: Number of ASL classes (26 for A-Z)
        """
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.model = None
    
    def create_custom_cnn(self) -> tf.keras.Model:
        """
        Create a custom CNN architecture optimized for ASL recognition
        
        Returns:
            Compiled Keras model
        """
        model = models.Sequential([
            # Input layer
            layers.Input(shape=self.input_shape),
            
            # First convolutional block
            layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Second convolutional block
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Third convolutional block
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Fourth convolutional block
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Global average pooling
            layers.GlobalAveragePooling2D(),
            
            # Dense layers
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            # Output layer
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        return model
    
    def create_transfer_learning_model(self, base_model_name: str = 'mobilenetv2') -> tf.keras.Model:
        """
        Create a transfer learning model using pre-trained backbone
        
        Args:
            base_model_name: Name of the base model ('mobilenetv2' or 'efficientnetb0')
            
        Returns:
            Compiled Keras model
        """
        # Load pre-trained base model
        if base_model_name.lower() == 'mobilenetv2':
            base_model = MobileNetV2(
                input_shape=self.input_shape,
                include_top=False,
                weights='imagenet'
            )
        elif base_model_name.lower() == 'efficientnetb0':
            base_model = EfficientNetB0(
                input_shape=self.input_shape,
                include_top=False,
                weights='imagenet'
            )
        else:
            raise ValueError(f"Unsupported base model: {base_model_name}")
        
        # Freeze base model layers
        base_model.trainable = False
        
        # Add custom classification head
        model = models.Sequential([
            base_model,
            layers.GlobalAveragePooling2D(),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        return model
    
    def compile_model(self, model: tf.keras.Model, learning_rate: float = 0.001) -> tf.keras.Model:
        """
        Compile the model with appropriate optimizer and loss function
        
        Args:
            model: Keras model to compile
            learning_rate: Learning rate for optimizer
            
        Returns:
            Compiled model
        """
        optimizer = optimizers.Adam(learning_rate=learning_rate)
        
        model.compile(
            optimizer=optimizer,
            loss='categorical_crossentropy',
            metrics=['accuracy', 'top_3_accuracy']
        )
        
        return model
    
    def create_model(self, model_type: str = 'custom', **kwargs) -> tf.keras.Model:
        """
        Create and compile a model
        
        Args:
            model_type: Type of model ('custom' or 'transfer')
            **kwargs: Additional arguments for model creation
            
        Returns:
            Compiled Keras model
        """
        if model_type == 'custom':
            model = self.create_custom_cnn()
        elif model_type == 'transfer':
            base_model_name = kwargs.get('base_model_name', 'mobilenetv2')
            model = self.create_transfer_learning_model(base_model_name)
        else:
            raise ValueError(f"Unsupported model type: {model_type}")
        
        # Compile model
        learning_rate = kwargs.get('learning_rate', 0.001)
        model = self.compile_model(model, learning_rate)
        
        self.model = model
        return model
    
    def get_callbacks(self, model_save_path: str = 'data/models/best_model.h5') -> list:
        """
        Get training callbacks
        
        Args:
            model_save_path: Path to save the best model
            
        Returns:
            List of Keras callbacks
        """
        callbacks_list = [
            # Save best model
            callbacks.ModelCheckpoint(
                filepath=model_save_path,
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                mode='max',
                verbose=1
            ),
            
            # Reduce learning rate on plateau
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            
            # Early stopping
            callbacks.EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            
            # TensorBoard logging
            callbacks.TensorBoard(
                log_dir='logs',
                histogram_freq=1,
                write_graph=True,
                write_images=True
            )
        ]
        
        return callbacks_list
    
    def print_model_summary(self):
        """Print model architecture summary"""
        if self.model is None:
            print("No model created yet. Call create_model() first.")
            return
        
        print("Model Architecture Summary:")
        print("=" * 50)
        self.model.summary()
        
        # Calculate model size
        total_params = self.model.count_params()
        print(f"\nTotal parameters: {total_params:,}")
        
        # Estimate model size in MB
        model_size_mb = (total_params * 4) / (1024 * 1024)  # Assuming float32
        print(f"Estimated model size: {model_size_mb:.2f} MB")
    
    def predict_single_image(self, image: np.ndarray, label_encoder=None) -> Tuple[str, float]:
        """
        Predict ASL letter for a single image
        
        Args:
            image: Input image (224, 224, 3)
            label_encoder: Label encoder for converting predictions to letters
            
        Returns:
            Tuple of (predicted_letter, confidence)
        """
        if self.model is None:
            raise ValueError("No model loaded. Create or load a model first.")
        
        # Ensure image is in correct format
        if image.shape != self.input_shape:
            image = tf.image.resize(image, self.input_shape[:2])
        
        # Add batch dimension
        image_batch = np.expand_dims(image, axis=0)
        
        # Make prediction
        predictions = self.model.predict(image_batch, verbose=0)
        predicted_class = np.argmax(predictions[0])
        confidence = np.max(predictions[0])
        
        # Convert to letter if label encoder is provided
        if label_encoder is not None:
            predicted_letter = label_encoder.inverse_transform([predicted_class])[0]
            return predicted_letter, confidence
        else:
            return str(predicted_class), confidence
    
    def load_model(self, model_path: str):
        """
        Load a saved model
        
        Args:
            model_path: Path to the saved model file
        """
        self.model = tf.keras.models.load_model(model_path)
        print(f"Model loaded from {model_path}")


if __name__ == "__main__":
    # Example usage
    asl_model = ASLModel(input_shape=(224, 224, 3), num_classes=26)
    
    # Create custom CNN
    model = asl_model.create_model(model_type='custom')
    asl_model.print_model_summary()
    
    # Create transfer learning model
    transfer_model = asl_model.create_model(
        model_type='transfer', 
        base_model_name='mobilenetv2'
    )
    asl_model.print_model_summary()
