"""
Data Preprocessing Pipeline for ASL Sign Language Recognition
Handles image preprocessing, data augmentation, and dataset preparation
"""

import cv2
import numpy as np
import os
import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import pickle
from typing import Tuple, List
import json


class ASLDataPreprocessor:
    """Preprocesses ASL training data for model training"""
    
    def __init__(self, raw_data_dir: str = "data/raw", processed_data_dir: str = "data/processed"):
        """
        Initialize the preprocessor
        
        Args:
            raw_data_dir: Directory containing raw collected images
            processed_data_dir: Directory to save processed data
        """
        self.raw_data_dir = raw_data_dir
        self.processed_data_dir = processed_data_dir
        self.image_size = (224, 224)
        self.asl_labels = [chr(i) for i in range(ord('A'), ord('Z') + 1)]
        
        # Create processed data directory
        os.makedirs(processed_data_dir, exist_ok=True)
        
        # Label encoder
        self.label_encoder = LabelEncoder()
        self.label_encoder.fit(self.asl_labels)
    
    def load_raw_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Load raw images and labels from the data directory
        
        Returns:
            Tuple of (images, labels) arrays
        """
        images = []
        labels = []
        
        print("Loading raw data...")
        
        for label in self.asl_labels:
            label_dir = os.path.join(self.raw_data_dir, label)
            
            if not os.path.exists(label_dir):
                print(f"Warning: Directory for label '{label}' not found")
                continue
            
            image_files = [f for f in os.listdir(label_dir) if f.endswith('.jpg')]
            print(f"Loading {len(image_files)} images for label '{label}'")
            
            for image_file in image_files:
                image_path = os.path.join(label_dir, image_file)
                
                # Load and preprocess image
                image = cv2.imread(image_path)
                if image is not None:
                    # Convert BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    
                    # Resize to target size
                    image = cv2.resize(image, self.image_size)
                    
                    # Normalize pixel values
                    image = image.astype(np.float32) / 255.0
                    
                    images.append(image)
                    labels.append(label)
        
        print(f"Loaded {len(images)} total images")
        return np.array(images), np.array(labels)
    
    def augment_data(self, images: np.ndarray, labels: np.ndarray, 
                    augmentation_factor: int = 3) -> Tuple[np.ndarray, np.ndarray]:
        """
        Apply data augmentation to increase dataset size
        
        Args:
            images: Input images
            labels: Corresponding labels
            augmentation_factor: Number of augmented versions per image
            
        Returns:
            Augmented images and labels
        """
        print(f"Applying data augmentation (factor: {augmentation_factor})...")
        
        # Data augmentation generator
        datagen = ImageDataGenerator(
            rotation_range=15,
            width_shift_range=0.1,
            height_shift_range=0.1,
            shear_range=0.1,
            zoom_range=0.1,
            horizontal_flip=False,  # Don't flip for sign language
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )
        
        augmented_images = []
        augmented_labels = []
        
        # Keep original data
        augmented_images.extend(images)
        augmented_labels.extend(labels)
        
        # Generate augmented data
        for i, (image, label) in enumerate(zip(images, labels)):
            # Reshape for generator
            image_batch = image.reshape((1,) + image.shape)
            
            # Generate augmented versions
            aug_iter = datagen.flow(image_batch, batch_size=1)
            
            for _ in range(augmentation_factor):
                aug_image = next(aug_iter)[0]
                augmented_images.append(aug_image)
                augmented_labels.append(label)
        
        print(f"Augmentation complete: {len(augmented_images)} total images")
        return np.array(augmented_images), np.array(augmented_labels)
    
    def prepare_dataset(self, test_size: float = 0.2, val_size: float = 0.1, 
                       augment: bool = True, augmentation_factor: int = 3) -> dict:
        """
        Prepare complete dataset for training
        
        Args:
            test_size: Fraction of data for testing
            val_size: Fraction of training data for validation
            augment: Whether to apply data augmentation
            augmentation_factor: Number of augmented versions per image
            
        Returns:
            Dictionary containing train/val/test splits
        """
        # Load raw data
        images, labels = self.load_raw_data()
        
        if len(images) == 0:
            raise ValueError("No images found. Please collect data first.")
        
        # Encode labels
        encoded_labels = self.label_encoder.transform(labels)
        
        # Split into train and test
        X_train, X_test, y_train, y_test = train_test_split(
            images, encoded_labels, test_size=test_size, 
            stratify=encoded_labels, random_state=42
        )
        
        # Apply augmentation to training data only
        if augment:
            X_train_aug, y_train_aug = self.augment_data(
                X_train, self.label_encoder.inverse_transform(y_train), 
                augmentation_factor
            )
            # Re-encode augmented labels
            y_train = self.label_encoder.transform(y_train_aug)
            X_train = X_train_aug
        
        # Split training data into train and validation
        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=val_size, 
            stratify=y_train, random_state=42
        )
        
        # Convert labels to categorical
        num_classes = len(self.asl_labels)
        y_train_cat = tf.keras.utils.to_categorical(y_train, num_classes)
        y_val_cat = tf.keras.utils.to_categorical(y_val, num_classes)
        y_test_cat = tf.keras.utils.to_categorical(y_test, num_classes)
        
        dataset = {
            'X_train': X_train,
            'y_train': y_train_cat,
            'X_val': X_val,
            'y_val': y_val_cat,
            'X_test': X_test,
            'y_test': y_test_cat,
            'label_encoder': self.label_encoder,
            'num_classes': num_classes
        }
        
        # Save dataset info
        self._save_dataset_info(dataset)
        
        print(f"Dataset prepared:")
        print(f"  Training samples: {len(X_train)}")
        print(f"  Validation samples: {len(X_val)}")
        print(f"  Test samples: {len(X_test)}")
        print(f"  Number of classes: {num_classes}")
        
        return dataset
    
    def _save_dataset_info(self, dataset: dict):
        """Save dataset information and label encoder"""
        info = {
            'num_classes': dataset['num_classes'],
            'image_size': self.image_size,
            'labels': self.asl_labels,
            'train_samples': len(dataset['X_train']),
            'val_samples': len(dataset['X_val']),
            'test_samples': len(dataset['X_test'])
        }
        
        # Save dataset info
        info_path = os.path.join(self.processed_data_dir, 'dataset_info.json')
        with open(info_path, 'w') as f:
            json.dump(info, f, indent=2)
        
        # Save label encoder
        encoder_path = os.path.join(self.processed_data_dir, 'label_encoder.pkl')
        with open(encoder_path, 'wb') as f:
            pickle.dump(dataset['label_encoder'], f)
        
        print(f"Dataset info saved to {info_path}")
        print(f"Label encoder saved to {encoder_path}")
    
    def save_processed_dataset(self, dataset: dict, filename: str = 'processed_dataset.npz'):
        """
        Save processed dataset to disk
        
        Args:
            dataset: Processed dataset dictionary
            filename: Output filename
        """
        filepath = os.path.join(self.processed_data_dir, filename)
        
        np.savez_compressed(
            filepath,
            X_train=dataset['X_train'],
            y_train=dataset['y_train'],
            X_val=dataset['X_val'],
            y_val=dataset['y_val'],
            X_test=dataset['X_test'],
            y_test=dataset['y_test']
        )
        
        print(f"Processed dataset saved to {filepath}")
    
    def load_processed_dataset(self, filename: str = 'processed_dataset.npz') -> dict:
        """
        Load processed dataset from disk
        
        Args:
            filename: Dataset filename
            
        Returns:
            Loaded dataset dictionary
        """
        filepath = os.path.join(self.processed_data_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Processed dataset not found: {filepath}")
        
        data = np.load(filepath)
        
        # Load label encoder
        encoder_path = os.path.join(self.processed_data_dir, 'label_encoder.pkl')
        with open(encoder_path, 'rb') as f:
            label_encoder = pickle.load(f)
        
        dataset = {
            'X_train': data['X_train'],
            'y_train': data['y_train'],
            'X_val': data['X_val'],
            'y_val': data['y_val'],
            'X_test': data['X_test'],
            'y_test': data['y_test'],
            'label_encoder': label_encoder,
            'num_classes': len(self.asl_labels)
        }
        
        print(f"Processed dataset loaded from {filepath}")
        return dataset


if __name__ == "__main__":
    # Example usage
    preprocessor = ASLDataPreprocessor()
    
    # Prepare dataset
    dataset = preprocessor.prepare_dataset(
        test_size=0.2,
        val_size=0.1,
        augment=True,
        augmentation_factor=2
    )
    
    # Save processed dataset
    preprocessor.save_processed_dataset(dataset)
