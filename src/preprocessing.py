"""
Data Preprocessing Pipeline for ASL Sign Language Recognition
Handles image preprocessing, data augmentation, and dataset preparation
"""

import cv2
import numpy as np
import os
import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import pickle
from typing import Tuple, List, Dict
import json


class ASLDataPreprocessor:
    """Preprocesses ASL training data for model training"""

    def __init__(self, raw_data_dir: str = "data/raw", processed_data_dir: str = "data/processed"):
        """
        Initialize the preprocessor

        Args:
            raw_data_dir: Directory containing raw collected images
            processed_data_dir: Directory to save processed data
        """
        self.raw_data_dir = raw_data_dir
        self.processed_data_dir = processed_data_dir
        self.image_size = (224, 224)
        self.asl_labels = [chr(i) for i in range(ord('A'), ord('Z') + 1)]

        # Create processed data directory
        os.makedirs(processed_data_dir, exist_ok=True)

        # Label encoder (will be fitted based on actual data)
        self.label_encoder = LabelEncoder()
        self.external_dataset_path = None
        self.dataset_source = "manual"  # "manual" or "external"
    
    def set_external_dataset(self, dataset_path: str):
        """
        Set external dataset path and switch to external mode

        Args:
            dataset_path: Path to external dataset directory
        """
        self.external_dataset_path = dataset_path
        self.dataset_source = "external"
        print(f"Switched to external dataset: {dataset_path}")

    def set_manual_collection(self):
        """Switch back to manual data collection mode"""
        self.external_dataset_path = None
        self.dataset_source = "manual"

    def process_external_dataset(self, dataset_path: str = None) -> bool:
        """
        Process external dataset and save preprocessed data

        Args:
            dataset_path: Path to external dataset (uses default if None)

        Returns:
            True if processing successful, False otherwise
        """
        try:
            # Use default path if not provided
            if dataset_path is None:
                dataset_path = "data/external/asl_alphabet_train"

            # Set external dataset
            self.set_external_dataset(dataset_path)

            # Prepare dataset with external data
            dataset = self.prepare_dataset(
                test_size=0.2,
                val_size=0.1,
                augment=True,
                augmentation_factor=2,  # Reduced for faster processing
                data_source="external",
                external_dataset_path=dataset_path
            )

            # Save individual arrays for compatibility
            np.save(os.path.join(self.processed_data_dir, 'X_train.npy'), dataset['X_train'])
            np.save(os.path.join(self.processed_data_dir, 'X_test.npy'), dataset['X_test'])
            np.save(os.path.join(self.processed_data_dir, 'y_train.npy'), dataset['y_train'])
            np.save(os.path.join(self.processed_data_dir, 'y_test.npy'), dataset['y_test'])

            # Save label encoder
            with open(os.path.join(self.processed_data_dir, 'label_encoder.pkl'), 'wb') as f:
                pickle.dump(self.label_encoder, f)

            print(f"✅ External dataset processed successfully")
            print(f"   Training samples: {len(dataset['X_train'])}")
            print(f"   Test samples: {len(dataset['X_test'])}")
            print(f"   Classes: {dataset['num_classes']}")

            return True

        except Exception as e:
            print(f"❌ Error processing external dataset: {e}")
            return False
        print("Switched to manual data collection mode")

    def load_raw_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Load raw images and labels from the data directory

        Returns:
            Tuple of (images, labels) arrays
        """
        if self.dataset_source == "external" and self.external_dataset_path:
            return self._load_external_data()
        else:
            return self._load_manual_data()

    def _load_manual_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """Load data from manual collection directory"""
        images = []
        labels = []

        print("Loading manual collection data...")

        for label in self.asl_labels:
            label_dir = os.path.join(self.raw_data_dir, label)

            if not os.path.exists(label_dir):
                print(f"Warning: Directory for label '{label}' not found")
                continue

            image_files = [f for f in os.listdir(label_dir) if f.endswith('.jpg')]
            print(f"Loading {len(image_files)} images for label '{label}'")

            for image_file in image_files:
                image_path = os.path.join(label_dir, image_file)

                # Load and preprocess image
                image = cv2.imread(image_path)
                if image is not None:
                    # Convert BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                    # Resize to target size
                    image = cv2.resize(image, self.image_size)

                    # Normalize pixel values
                    image = image.astype(np.float32) / 255.0

                    images.append(image)
                    labels.append(label)

        print(f"Loaded {len(images)} total images from manual collection")
        return np.array(images), np.array(labels)

    def _load_external_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """Load data from external dataset directory"""
        images = []
        labels = []

        print(f"Loading external dataset from: {self.external_dataset_path}")

        # Get all class directories
        from .utils import get_external_dataset_classes
        classes = get_external_dataset_classes(self.external_dataset_path)

        if not classes:
            print("Warning: No classes found in external dataset")
            return np.array(images), np.array(labels)

        print(f"Found {len(classes)} classes: {classes}")

        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}

        for class_name in classes:
            class_dir = os.path.join(self.external_dataset_path, class_name)

            if not os.path.isdir(class_dir):
                continue

            image_files = []
            for file in os.listdir(class_dir):
                file_path = os.path.join(class_dir, file)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file.lower())
                    if ext in valid_extensions:
                        image_files.append(file)

            print(f"Loading {len(image_files)} images for class '{class_name}'")

            for image_file in image_files:
                image_path = os.path.join(class_dir, image_file)

                # Load and preprocess image
                image = cv2.imread(image_path)
                if image is not None:
                    # Convert BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                    # Resize to target size
                    image = cv2.resize(image, self.image_size)

                    # Normalize pixel values
                    image = image.astype(np.float32) / 255.0

                    images.append(image)
                    labels.append(class_name)

        print(f"Loaded {len(images)} total images from external dataset")
        return np.array(images), np.array(labels)

    def validate_external_dataset(self, dataset_path: str) -> Tuple[bool, str, Dict]:
        """
        Validate external dataset structure and contents

        Args:
            dataset_path: Path to external dataset directory

        Returns:
            Tuple of (is_valid, message, dataset_info)
        """
        if not os.path.exists(dataset_path):
            return False, f"Dataset path does not exist: {dataset_path}", {}

        if not os.path.isdir(dataset_path):
            return False, f"Dataset path is not a directory: {dataset_path}", {}

        # Check for image files
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend([f for f in os.listdir(dataset_path) if f.lower().endswith(ext)])

        if len(image_files) == 0:
            return False, "No image files found in dataset directory", {}

        # Analyze dataset structure
        dataset_info = {
            'total_files': len(image_files),
            'letters_found': set(),
            'file_structure': 'unknown',
            'samples_per_letter': {},
            'invalid_files': []
        }

        # Check if it's a flat structure with letter prefixes (like A_test.jpg)
        flat_structure_count = 0
        for file in image_files:
            # Extract letter from filename
            if '_' in file:
                letter_part = file.split('_')[0].upper()
                if len(letter_part) == 1 and letter_part in self.asl_labels:
                    dataset_info['letters_found'].add(letter_part)
                    dataset_info['samples_per_letter'][letter_part] = dataset_info['samples_per_letter'].get(letter_part, 0) + 1
                    flat_structure_count += 1
                elif letter_part.upper() in ['NOTHING', 'SPACE']:
                    # Skip non-letter classes for now
                    continue
                else:
                    dataset_info['invalid_files'].append(file)
            else:
                # Try to extract letter from start of filename
                if len(file) > 0 and file[0].upper() in self.asl_labels:
                    letter = file[0].upper()
                    dataset_info['letters_found'].add(letter)
                    dataset_info['samples_per_letter'][letter] = dataset_info['samples_per_letter'].get(letter, 0) + 1
                    flat_structure_count += 1
                else:
                    dataset_info['invalid_files'].append(file)

        if flat_structure_count > 0:
            dataset_info['file_structure'] = 'flat_with_prefixes'

        # Check if it's a directory structure (A/, B/, C/, etc.)
        subdirs = [d for d in os.listdir(dataset_path) if os.path.isdir(os.path.join(dataset_path, d))]
        dir_structure_count = 0
        for subdir in subdirs:
            if len(subdir) == 1 and subdir.upper() in self.asl_labels:
                letter = subdir.upper()
                dataset_info['letters_found'].add(letter)
                subdir_path = os.path.join(dataset_path, subdir)
                subdir_images = [f for f in os.listdir(subdir_path)
                               if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
                dataset_info['samples_per_letter'][letter] = len(subdir_images)
                dir_structure_count += 1

        if dir_structure_count > 0:
            dataset_info['file_structure'] = 'directory_per_letter'

        # Validation results
        letters_found = len(dataset_info['letters_found'])
        if letters_found == 0:
            return False, "No valid ASL letter classes found in dataset", dataset_info

        if letters_found < 5:
            message = f"Warning: Only {letters_found} letter classes found. Minimum 5 recommended for training."
        else:
            message = f"Dataset validated successfully. Found {letters_found} letter classes with {len(image_files)} total images."

        return True, message, dataset_info

    def load_external_dataset(self, dataset_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        Load images and labels from external dataset

        Args:
            dataset_path: Path to external dataset directory

        Returns:
            Tuple of (images, labels) arrays
        """
        # First validate the dataset
        is_valid, message, dataset_info = self.validate_external_dataset(dataset_path)

        if not is_valid:
            raise ValueError(f"Invalid dataset: {message}")

        print(f"Loading external dataset: {message}")

        images = []
        labels = []

        if dataset_info['file_structure'] == 'flat_with_prefixes':
            # Load from flat structure with filename prefixes
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                image_files.extend([f for f in os.listdir(dataset_path) if f.lower().endswith(ext)])

            for image_file in image_files:
                # Extract letter from filename
                if '_' in image_file:
                    letter_part = image_file.split('_')[0].upper()
                else:
                    letter_part = image_file[0].upper()

                if len(letter_part) == 1 and letter_part in self.asl_labels:
                    image_path = os.path.join(dataset_path, image_file)

                    # Load and preprocess image
                    image = cv2.imread(image_path)
                    if image is not None:
                        # Convert BGR to RGB
                        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                        # Resize to target size
                        image = cv2.resize(image, self.image_size)

                        # Normalize pixel values
                        image = image.astype(np.float32) / 255.0

                        images.append(image)
                        labels.append(letter_part)
                    else:
                        print(f"Warning: Could not load image {image_path}")

        elif dataset_info['file_structure'] == 'directory_per_letter':
            # Load from directory structure (A/, B/, C/, etc.)
            for letter in dataset_info['letters_found']:
                letter_dir = os.path.join(dataset_path, letter)
                if os.path.exists(letter_dir):
                    image_files = [f for f in os.listdir(letter_dir)
                                 if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]

                    print(f"Loading {len(image_files)} images for letter '{letter}'")

                    for image_file in image_files:
                        image_path = os.path.join(letter_dir, image_file)

                        # Load and preprocess image
                        image = cv2.imread(image_path)
                        if image is not None:
                            # Convert BGR to RGB
                            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                            # Resize to target size
                            image = cv2.resize(image, self.image_size)

                            # Normalize pixel values
                            image = image.astype(np.float32) / 255.0

                            images.append(image)
                            labels.append(letter)
                        else:
                            print(f"Warning: Could not load image {image_path}")

        else:
            raise ValueError(f"Unsupported dataset structure: {dataset_info['file_structure']}")

        print(f"Loaded {len(images)} images from external dataset")
        return np.array(images), np.array(labels)
    
    def augment_data(self, images: np.ndarray, labels: np.ndarray, 
                    augmentation_factor: int = 3) -> Tuple[np.ndarray, np.ndarray]:
        """
        Apply data augmentation to increase dataset size
        
        Args:
            images: Input images
            labels: Corresponding labels
            augmentation_factor: Number of augmented versions per image
            
        Returns:
            Augmented images and labels
        """
        print(f"Applying data augmentation (factor: {augmentation_factor})...")
        
        # Data augmentation generator
        datagen = ImageDataGenerator(
            rotation_range=15,
            width_shift_range=0.1,
            height_shift_range=0.1,
            shear_range=0.1,
            zoom_range=0.1,
            horizontal_flip=False,  # Don't flip for sign language
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )
        
        augmented_images = []
        augmented_labels = []
        
        # Keep original data
        augmented_images.extend(images)
        augmented_labels.extend(labels)
        
        # Generate augmented data
        for i, (image, label) in enumerate(zip(images, labels)):
            # Reshape for generator
            image_batch = image.reshape((1,) + image.shape)
            
            # Generate augmented versions
            aug_iter = datagen.flow(image_batch, batch_size=1)
            
            for _ in range(augmentation_factor):
                aug_image = next(aug_iter)[0]
                augmented_images.append(aug_image)
                augmented_labels.append(label)
        
        print(f"Augmentation complete: {len(augmented_images)} total images")
        return np.array(augmented_images), np.array(augmented_labels)
    
    def prepare_dataset(self, test_size: float = 0.2, val_size: float = 0.1,
                       augment: bool = True, augmentation_factor: int = 3,
                       data_source: str = "manual", external_dataset_path: str = None) -> dict:
        """
        Prepare complete dataset for training

        Args:
            test_size: Fraction of data for testing
            val_size: Fraction of training data for validation
            augment: Whether to apply data augmentation
            augmentation_factor: Number of augmented versions per image
            data_source: "manual" for collected data or "external" for external dataset
            external_dataset_path: Path to external dataset (required if data_source="external")

        Returns:
            Dictionary containing train/val/test splits
        """
        # Load data based on source
        if data_source == "manual":
            print("Loading manually collected data...")
            images, labels = self.load_raw_data()
        elif data_source == "external":
            if external_dataset_path is None:
                raise ValueError("external_dataset_path is required when data_source='external'")
            # Set external dataset and use the internal loading method
            self.set_external_dataset(external_dataset_path)
            images, labels = self.load_raw_data()
        else:
            raise ValueError(f"Invalid data_source: {data_source}. Must be 'manual' or 'external'")
        
        if len(images) == 0:
            raise ValueError("No images found. Please collect data first.")
        
        # Fit and encode labels based on actual data
        unique_labels = sorted(list(set(labels)))
        self.label_encoder.fit(unique_labels)
        encoded_labels = self.label_encoder.transform(labels)

        print(f"Found {len(unique_labels)} unique classes: {unique_labels}")

        # Check if we have enough samples per class for stratified split
        unique_labels, label_counts = np.unique(encoded_labels, return_counts=True)
        min_samples_per_class = np.min(label_counts)

        print(f"Minimum samples per class: {min_samples_per_class}")

        # Adjust split strategy based on available data
        if min_samples_per_class < 2:
            print("Warning: Some classes have only 1 sample. Using simple random split without stratification.")
            # Calculate appropriate test size to ensure we have enough samples
            total_samples = len(images)
            num_classes = len(unique_labels)
            # Ensure test set has at least as many samples as classes, but not more than 50% of data
            min_test_samples = max(num_classes, int(total_samples * 0.1))
            max_test_samples = int(total_samples * 0.5)
            test_samples = min(min_test_samples, max_test_samples)
            adjusted_test_size = test_samples / total_samples

            print(f"Adjusting test size to {adjusted_test_size:.2f} ({test_samples} samples) for {num_classes} classes")

            # Use simple random split without stratification
            X_train, X_test, y_train, y_test = train_test_split(
                images, encoded_labels, test_size=adjusted_test_size,
                random_state=42
            )
        elif min_samples_per_class < 3:
            print("Warning: Limited samples per class. Reducing test size and using stratified split.")
            # Reduce test size for very small datasets
            adjusted_test_size = min(test_size, 0.2)
            X_train, X_test, y_train, y_test = train_test_split(
                images, encoded_labels, test_size=adjusted_test_size,
                stratify=encoded_labels, random_state=42
            )
        else:
            # Normal stratified split
            X_train, X_test, y_train, y_test = train_test_split(
                images, encoded_labels, test_size=test_size,
                stratify=encoded_labels, random_state=42
            )
        
        # Apply augmentation to training data only
        if augment and len(X_train) > 0:
            X_train_aug, y_train_aug = self.augment_data(
                X_train, self.label_encoder.inverse_transform(y_train),
                augmentation_factor
            )
            # Re-encode augmented labels
            y_train = self.label_encoder.transform(y_train_aug)
            X_train = X_train_aug
        
        # Split training data into train and validation
        # Check if we have enough samples for validation split
        if len(X_train) > 0:
            unique_train_labels, train_label_counts = np.unique(y_train, return_counts=True)
            min_train_samples = np.min(train_label_counts)
            total_train_samples = len(X_train)
            num_train_classes = len(unique_train_labels)

            # Calculate appropriate validation size
            min_val_samples = max(num_train_classes, int(total_train_samples * 0.1))
            max_val_samples = int(total_train_samples * 0.3)

            if min_train_samples < 2 or total_train_samples < num_train_classes * 2:
                print("Warning: Insufficient training samples for validation split. Using training data for validation.")
                # Use training data as validation (not ideal but necessary for very small datasets)
                X_val, y_val = X_train.copy(), y_train.copy()
            else:
                # Calculate validation size
                val_samples = min(min_val_samples, max_val_samples)
                adjusted_val_size = val_samples / total_train_samples

                print(f"Using validation size: {adjusted_val_size:.2f} ({val_samples} samples)")

                try:
                    # Try stratified split first
                    X_train, X_val, y_train, y_val = train_test_split(
                        X_train, y_train, test_size=adjusted_val_size,
                        stratify=y_train, random_state=42
                    )
                except ValueError:
                    # Fall back to random split if stratified fails
                    print("Stratified split failed, using random split for validation.")
                    X_train, X_val, y_train, y_val = train_test_split(
                        X_train, y_train, test_size=adjusted_val_size,
                        random_state=42
                    )
        else:
            # No training data available
            X_val, y_val = np.array([]), np.array([])
        
        # Convert labels to categorical - use actual number of classes from data
        unique_labels = sorted(list(set(labels)))
        num_classes = len(unique_labels)
        y_train_cat = tf.keras.utils.to_categorical(y_train, num_classes)
        y_val_cat = tf.keras.utils.to_categorical(y_val, num_classes)
        y_test_cat = tf.keras.utils.to_categorical(y_test, num_classes)
        
        dataset = {
            'X_train': X_train,
            'y_train': y_train_cat,
            'X_val': X_val,
            'y_val': y_val_cat,
            'X_test': X_test,
            'y_test': y_test_cat,
            'label_encoder': self.label_encoder,
            'num_classes': num_classes
        }
        
        # Save dataset info
        self._save_dataset_info(dataset)
        
        print(f"Dataset prepared:")
        print(f"  Training samples: {len(X_train)}")
        print(f"  Validation samples: {len(X_val)}")
        print(f"  Test samples: {len(X_test)}")
        print(f"  Number of classes: {num_classes}")
        
        return dataset
    
    def _save_dataset_info(self, dataset: dict):
        """Save dataset information and label encoder"""
        info = {
            'num_classes': dataset['num_classes'],
            'image_size': self.image_size,
            'labels': self.asl_labels,
            'train_samples': len(dataset['X_train']),
            'val_samples': len(dataset['X_val']),
            'test_samples': len(dataset['X_test'])
        }
        
        # Save dataset info
        info_path = os.path.join(self.processed_data_dir, 'dataset_info.json')
        with open(info_path, 'w') as f:
            json.dump(info, f, indent=2)
        
        # Save label encoder
        encoder_path = os.path.join(self.processed_data_dir, 'label_encoder.pkl')
        with open(encoder_path, 'wb') as f:
            pickle.dump(dataset['label_encoder'], f)
        
        print(f"Dataset info saved to {info_path}")
        print(f"Label encoder saved to {encoder_path}")
    
    def save_processed_dataset(self, dataset: dict, filename: str = 'processed_dataset.npz'):
        """
        Save processed dataset to disk
        
        Args:
            dataset: Processed dataset dictionary
            filename: Output filename
        """
        filepath = os.path.join(self.processed_data_dir, filename)
        
        np.savez_compressed(
            filepath,
            X_train=dataset['X_train'],
            y_train=dataset['y_train'],
            X_val=dataset['X_val'],
            y_val=dataset['y_val'],
            X_test=dataset['X_test'],
            y_test=dataset['y_test']
        )
        
        print(f"Processed dataset saved to {filepath}")
    
    def load_processed_dataset(self, filename: str = 'processed_dataset.npz') -> dict:
        """
        Load processed dataset from disk
        
        Args:
            filename: Dataset filename
            
        Returns:
            Loaded dataset dictionary
        """
        filepath = os.path.join(self.processed_data_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Processed dataset not found: {filepath}")
        
        data = np.load(filepath)
        
        # Load label encoder
        encoder_path = os.path.join(self.processed_data_dir, 'label_encoder.pkl')
        with open(encoder_path, 'rb') as f:
            label_encoder = pickle.load(f)
        
        dataset = {
            'X_train': data['X_train'],
            'y_train': data['y_train'],
            'X_val': data['X_val'],
            'y_val': data['y_val'],
            'X_test': data['X_test'],
            'y_test': data['y_test'],
            'label_encoder': label_encoder,
            'num_classes': len(self.asl_labels)
        }
        
        print(f"Processed dataset loaded from {filepath}")
        return dataset


if __name__ == "__main__":
    # Example usage
    preprocessor = ASLDataPreprocessor()
    
    # Prepare dataset
    dataset = preprocessor.prepare_dataset(
        test_size=0.2,
        val_size=0.1,
        augment=True,
        augmentation_factor=2
    )
    
    # Save processed dataset
    preprocessor.save_processed_dataset(dataset)
