"""
Model Training Pipeline for ASL Sign Language Recognition
Handles model training with progress tracking, validation, and checkpointing
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
import os
import json
import time
from datetime import datetime
from typing import Dict, Tuple, Optional
import pickle

try:
    from .model import ASLModel
    from .preprocessing import ASLDataPreprocessor
except ImportError:
    from model import ASLModel
    from preprocessing import ASLDataPreprocessor


class ASLTrainer:
    """Handles training of ASL recognition models"""
    
    def __init__(self, model_dir: str = "data/models", log_dir: str = "logs"):
        """
        Initialize the trainer
        
        Args:
            model_dir: Directory to save trained models
            log_dir: Directory for training logs
        """
        self.model_dir = model_dir
        self.log_dir = log_dir
        
        # Create directories
        os.makedirs(model_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        
        self.model = None
        self.training_history = None
        self.dataset = None

        # Training data
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None

    def load_data(self, processed_dir: str = "data/processed") -> bool:
        """
        Load preprocessed training data

        Args:
            processed_dir: Directory containing processed data files

        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            # Load training data arrays
            self.X_train = np.load(os.path.join(processed_dir, 'X_train.npy'))
            self.X_test = np.load(os.path.join(processed_dir, 'X_test.npy'))
            self.y_train = np.load(os.path.join(processed_dir, 'y_train.npy'))
            self.y_test = np.load(os.path.join(processed_dir, 'y_test.npy'))

            print(f"✅ Training data loaded:")
            print(f"   Training samples: {len(self.X_train)}")
            print(f"   Test samples: {len(self.X_test)}")
            print(f"   Input shape: {self.X_train.shape[1:]}")
            print(f"   Number of classes: {len(np.unique(self.y_train))}")

            return True

        except Exception as e:
            print(f"❌ Error loading training data: {e}")
            return False
    
    def prepare_data(self, augment: bool = True, augmentation_factor: int = 3,
                    data_source: str = "manual", external_dataset_path: str = None) -> Dict:
        """
        Prepare training data

        Args:
            augment: Whether to apply data augmentation
            augmentation_factor: Number of augmented versions per image
            data_source: "manual" for collected data or "external" for external dataset
            external_dataset_path: Path to external dataset (required if data_source="external")

        Returns:
            Prepared dataset dictionary
        """
        print("Preparing training data...")
        preprocessor = ASLDataPreprocessor()

        # Create unique processed file name based on data source
        if data_source == "external" and external_dataset_path:
            dataset_name = os.path.basename(external_dataset_path.rstrip('/'))
            processed_file = os.path.join(preprocessor.processed_data_dir, f'processed_dataset_{dataset_name}.npz')
        else:
            processed_file = os.path.join(preprocessor.processed_data_dir, 'processed_dataset.npz')

        # For external datasets, always reprocess to ensure compatibility
        if data_source == "external" or not os.path.exists(processed_file):
            print(f"Creating new processed dataset from {data_source} data...")
            self.dataset = preprocessor.prepare_dataset(
                test_size=0.2,
                val_size=0.1,
                augment=augment,
                augmentation_factor=augmentation_factor,
                data_source=data_source,
                external_dataset_path=external_dataset_path
            )
            # Save with appropriate filename
            filename = os.path.basename(processed_file)
            preprocessor.save_processed_dataset(self.dataset, filename)
        else:
            print("Loading existing processed dataset...")
            self.dataset = preprocessor.load_processed_dataset()
        
        return self.dataset
    
    def create_model(self, model_type: str = 'custom', num_classes: int = None, **kwargs) -> tf.keras.Model:
        """
        Create and compile model

        Args:
            model_type: Type of model ('custom' or 'transfer')
            num_classes: Number of classes (auto-detected if None)
            **kwargs: Additional model parameters

        Returns:
            Compiled Keras model
        """
        # Determine number of classes
        if num_classes is None:
            if self.dataset is not None:
                num_classes = self.dataset['num_classes']
            elif self.y_train is not None:
                num_classes = len(np.unique(self.y_train))
            else:
                raise ValueError("Cannot determine number of classes. Provide num_classes or prepare/load data first.")

        print(f"Creating {model_type} model with {num_classes} classes...")

        asl_model = ASLModel(
            input_shape=(224, 224, 3),
            num_classes=num_classes
        )

        self.model = asl_model.create_model(model_type=model_type, **kwargs)
        asl_model.print_model_summary()
        
        return self.model
    
    def train_model(self, epochs: int = 50, batch_size: int = 32,
                   validation_split: float = 0.2, save_path: str = None) -> str:
        """
        Train the model

        Args:
            epochs: Number of training epochs
            batch_size: Training batch size
            validation_split: Fraction of training data to use for validation
            save_path: Path to save the trained model

        Returns:
            Path to saved model
        """
        # Check if we have loaded data or prepared dataset
        if self.X_train is not None and self.y_train is not None:
            # Use loaded data
            X_train, y_train = self.X_train, self.y_train
            X_test, y_test = self.X_test, self.y_test

            # Detect number of classes (handle both one-hot and integer labels)
            if len(y_train.shape) > 1 and y_train.shape[1] > 1:
                # One-hot encoded labels
                num_classes = y_train.shape[1]
            else:
                # Integer labels
                num_classes = len(np.unique(y_train))

        elif self.dataset is not None:
            # Use prepared dataset
            X_train = self.dataset['X_train']
            y_train = self.dataset['y_train']
            X_test = self.dataset['X_test']
            y_test = self.dataset['y_test']
            num_classes = self.dataset['num_classes']
        else:
            raise ValueError("No data available. Call load_data() or prepare_data() first.")

        # Create model if not exists
        if self.model is None:
            self.model = self.create_model(num_classes=num_classes)

        # Generate save path if not provided
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_name = f"asl_model_{timestamp}"
            save_path = os.path.join(self.model_dir, f"{model_name}.h5")

        model_save_path = save_path
        
        print(f"Starting training for {epochs} epochs...")
        print(f"Model will be saved to: {model_save_path}")
        
        # Get callbacks
        asl_model = ASLModel()
        callbacks = asl_model.get_callbacks(model_save_path)
        
        # Record training start time
        start_time = time.time()
        
        # Train the model
        self.training_history = self.model.fit(
            X_train,
            y_train,
            batch_size=batch_size,
            epochs=epochs,
            validation_split=validation_split,
            callbacks=callbacks,
            verbose=1
        )
        
        # Record training time
        training_time = time.time() - start_time
        
        print(f"Training completed in {training_time:.2f} seconds")
        print(f"Model saved to: {model_save_path}")

        # Save training history and metadata
        model_name = os.path.splitext(os.path.basename(model_save_path))[0]
        self._save_training_metadata(model_name, training_time, epochs, batch_size)

        return model_save_path
    
    def evaluate_model(self, model_path: str = None) -> Dict:
        """
        Evaluate model performance on test set
        
        Args:
            model_path: Path to saved model (if None, uses current model)
            
        Returns:
            Evaluation metrics dictionary
        """
        if model_path:
            self.model = tf.keras.models.load_model(model_path)
            print(f"Loaded model from {model_path}")
        
        if self.model is None:
            raise ValueError("No model available for evaluation")
        
        if self.dataset is None:
            raise ValueError("Dataset not available. Call prepare_data() first.")
        
        print("Evaluating model on test set...")
        
        # Evaluate on test set
        test_loss, test_accuracy, test_top3_accuracy = self.model.evaluate(
            self.dataset['X_test'],
            self.dataset['y_test'],
            verbose=1
        )
        
        # Get predictions
        y_pred = self.model.predict(self.dataset['X_test'])
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(self.dataset['y_test'], axis=1)
        
        # Generate classification report
        label_names = [chr(i) for i in range(ord('A'), ord('Z') + 1)]

        # Get unique classes present in both true and predicted
        all_classes = np.unique(np.concatenate([y_true_classes, y_pred_classes]))

        class_report = classification_report(
            y_true_classes, y_pred_classes,
            labels=all_classes,
            target_names=[label_names[i] for i in all_classes],
            output_dict=True,
            zero_division=0
        )
        
        # Create confusion matrix
        cm = confusion_matrix(y_true_classes, y_pred_classes)
        
        evaluation_results = {
            'test_loss': test_loss,
            'test_accuracy': test_accuracy,
            'test_top3_accuracy': test_top3_accuracy,
            'classification_report': class_report,
            'confusion_matrix': cm.tolist(),
            'label_names': label_names
        }
        
        # Print results
        print(f"\nTest Results:")
        print(f"Test Loss: {test_loss:.4f}")
        print(f"Test Accuracy: {test_accuracy:.4f}")
        print(f"Test Top-3 Accuracy: {test_top3_accuracy:.4f}")
        
        return evaluation_results
    
    def plot_training_history(self, save_path: str = None):
        """
        Plot training history
        
        Args:
            save_path: Path to save the plot
        """
        if self.training_history is None:
            print("No training history available")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Plot training & validation accuracy
        axes[0, 0].plot(self.training_history.history['accuracy'], label='Training Accuracy')
        axes[0, 0].plot(self.training_history.history['val_accuracy'], label='Validation Accuracy')
        axes[0, 0].set_title('Model Accuracy')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Plot training & validation loss
        axes[0, 1].plot(self.training_history.history['loss'], label='Training Loss')
        axes[0, 1].plot(self.training_history.history['val_loss'], label='Validation Loss')
        axes[0, 1].set_title('Model Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Plot top-3 accuracy
        axes[1, 0].plot(self.training_history.history['top_3_accuracy'], label='Training Top-3 Accuracy')
        axes[1, 0].plot(self.training_history.history['val_top_3_accuracy'], label='Validation Top-3 Accuracy')
        axes[1, 0].set_title('Model Top-3 Accuracy')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Top-3 Accuracy')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # Plot learning rate (if available)
        if 'lr' in self.training_history.history:
            axes[1, 1].plot(self.training_history.history['lr'], label='Learning Rate')
            axes[1, 1].set_title('Learning Rate')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Learning Rate')
            axes[1, 1].set_yscale('log')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        else:
            axes[1, 1].text(0.5, 0.5, 'Learning Rate\nNot Available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Training history plot saved to {save_path}")
        
        plt.show()
    
    def plot_confusion_matrix(self, evaluation_results: Dict, save_path: str = None):
        """
        Plot confusion matrix
        
        Args:
            evaluation_results: Results from evaluate_model()
            save_path: Path to save the plot
        """
        cm = np.array(evaluation_results['confusion_matrix'])
        label_names = evaluation_results['label_names']
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=label_names, yticklabels=label_names)
        plt.title('Confusion Matrix - ASL Letter Recognition')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Confusion matrix plot saved to {save_path}")
        
        plt.show()
    
    def _get_dataset_info(self) -> dict:
        """Get dataset information for metadata"""
        if self.dataset is not None:
            return {
                'train_samples': len(self.dataset['X_train']),
                'val_samples': len(self.dataset.get('X_val', [])),
                'test_samples': len(self.dataset['X_test']),
                'num_classes': self.dataset['num_classes']
            }
        elif self.X_train is not None:
            return {
                'train_samples': len(self.X_train),
                'test_samples': len(self.X_test) if self.X_test is not None else 0,
                'num_classes': self.y_train.shape[1] if len(self.y_train.shape) > 1 else len(np.unique(self.y_train))
            }
        else:
            return {'train_samples': 0, 'test_samples': 0, 'num_classes': 0}

    def _save_training_metadata(self, model_name: str, training_time: float,
                              epochs: int, batch_size: int):
        """Save training metadata"""
        metadata = {
            'model_name': model_name,
            'training_time_seconds': training_time,
            'epochs': epochs,
            'batch_size': batch_size,
            'timestamp': datetime.now().isoformat(),
            'dataset_info': self._get_dataset_info()
        }
        
        # Save training history
        if self.training_history:
            metadata['training_history'] = {
                key: [float(val) for val in values] 
                for key, values in self.training_history.history.items()
            }
        
        metadata_path = os.path.join(self.model_dir, f"{model_name}_metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"Training metadata saved to {metadata_path}")


if __name__ == "__main__":
    # Example usage
    trainer = ASLTrainer()
    
    # Prepare data
    dataset = trainer.prepare_data(augment=True, augmentation_factor=2)
    
    # Create and train model
    model = trainer.create_model(model_type='custom')
    history = trainer.train_model(epochs=30, batch_size=32)
    
    # Evaluate model
    results = trainer.evaluate_model()
    
    # Plot results
    trainer.plot_training_history()
    trainer.plot_confusion_matrix(results)
