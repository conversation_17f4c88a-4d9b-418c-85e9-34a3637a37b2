"""
Model Training Pipeline for ASL Sign Language Recognition
Handles model training with progress tracking, validation, and checkpointing
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
import os
import json
import time
from datetime import datetime
from typing import Dict, Tuple, Optional
import pickle

try:
    from .model import ASLModel
    from .preprocessing import ASLDataPreprocessor
except ImportError:
    from model import ASLModel
    from preprocessing import ASLDataPreprocessor


class ASLTrainer:
    """Handles training of ASL recognition models"""
    
    def __init__(self, model_dir: str = "data/models", log_dir: str = "logs"):
        """
        Initialize the trainer
        
        Args:
            model_dir: Directory to save trained models
            log_dir: Directory for training logs
        """
        self.model_dir = model_dir
        self.log_dir = log_dir
        
        # Create directories
        os.makedirs(model_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        
        self.model = None
        self.training_history = None
        self.dataset = None
    
    def prepare_data(self, augment: bool = True, augmentation_factor: int = 3,
                    data_source: str = "manual", external_dataset_path: str = None) -> Dict:
        """
        Prepare training data

        Args:
            augment: Whether to apply data augmentation
            augmentation_factor: Number of augmented versions per image
            data_source: "manual" for collected data or "external" for external dataset
            external_dataset_path: Path to external dataset (required if data_source="external")

        Returns:
            Prepared dataset dictionary
        """
        print("Preparing training data...")
        preprocessor = ASLDataPreprocessor()

        # Create unique processed file name based on data source
        if data_source == "external" and external_dataset_path:
            dataset_name = os.path.basename(external_dataset_path.rstrip('/'))
            processed_file = os.path.join(preprocessor.processed_data_dir, f'processed_dataset_{dataset_name}.npz')
        else:
            processed_file = os.path.join(preprocessor.processed_data_dir, 'processed_dataset.npz')

        # For external datasets, always reprocess to ensure compatibility
        if data_source == "external" or not os.path.exists(processed_file):
            print(f"Creating new processed dataset from {data_source} data...")
            self.dataset = preprocessor.prepare_dataset(
                test_size=0.2,
                val_size=0.1,
                augment=augment,
                augmentation_factor=augmentation_factor,
                data_source=data_source,
                external_dataset_path=external_dataset_path
            )
            # Save with appropriate filename
            filename = os.path.basename(processed_file)
            preprocessor.save_processed_dataset(self.dataset, filename)
        else:
            print("Loading existing processed dataset...")
            self.dataset = preprocessor.load_processed_dataset()
        
        return self.dataset
    
    def create_model(self, model_type: str = 'custom', **kwargs) -> tf.keras.Model:
        """
        Create and compile model
        
        Args:
            model_type: Type of model ('custom' or 'transfer')
            **kwargs: Additional model parameters
            
        Returns:
            Compiled Keras model
        """
        if self.dataset is None:
            raise ValueError("Dataset not prepared. Call prepare_data() first.")
        
        print(f"Creating {model_type} model...")
        
        asl_model = ASLModel(
            input_shape=(224, 224, 3),
            num_classes=self.dataset['num_classes']
        )
        
        self.model = asl_model.create_model(model_type=model_type, **kwargs)
        asl_model.print_model_summary()
        
        return self.model
    
    def train_model(self, epochs: int = 50, batch_size: int = 32, 
                   model_name: str = None) -> tf.keras.callbacks.History:
        """
        Train the model
        
        Args:
            epochs: Number of training epochs
            batch_size: Training batch size
            model_name: Name for saving the model
            
        Returns:
            Training history
        """
        if self.model is None:
            raise ValueError("Model not created. Call create_model() first.")
        
        if self.dataset is None:
            raise ValueError("Dataset not prepared. Call prepare_data() first.")
        
        # Generate model name if not provided
        if model_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_name = f"asl_model_{timestamp}"
        
        model_save_path = os.path.join(self.model_dir, f"{model_name}.h5")
        
        print(f"Starting training for {epochs} epochs...")
        print(f"Model will be saved to: {model_save_path}")
        
        # Get callbacks
        asl_model = ASLModel()
        callbacks = asl_model.get_callbacks(model_save_path)
        
        # Record training start time
        start_time = time.time()
        
        # Train the model
        self.training_history = self.model.fit(
            self.dataset['X_train'],
            self.dataset['y_train'],
            batch_size=batch_size,
            epochs=epochs,
            validation_data=(self.dataset['X_val'], self.dataset['y_val']),
            callbacks=callbacks,
            verbose=1
        )
        
        # Record training time
        training_time = time.time() - start_time
        
        print(f"Training completed in {training_time:.2f} seconds")
        
        # Save training history and metadata
        self._save_training_metadata(model_name, training_time, epochs, batch_size)
        
        return self.training_history
    
    def evaluate_model(self, model_path: str = None) -> Dict:
        """
        Evaluate model performance on test set
        
        Args:
            model_path: Path to saved model (if None, uses current model)
            
        Returns:
            Evaluation metrics dictionary
        """
        if model_path:
            self.model = tf.keras.models.load_model(model_path)
            print(f"Loaded model from {model_path}")
        
        if self.model is None:
            raise ValueError("No model available for evaluation")
        
        if self.dataset is None:
            raise ValueError("Dataset not available. Call prepare_data() first.")
        
        print("Evaluating model on test set...")
        
        # Evaluate on test set
        test_loss, test_accuracy, test_top3_accuracy = self.model.evaluate(
            self.dataset['X_test'],
            self.dataset['y_test'],
            verbose=1
        )
        
        # Get predictions
        y_pred = self.model.predict(self.dataset['X_test'])
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(self.dataset['y_test'], axis=1)
        
        # Generate classification report
        label_names = [chr(i) for i in range(ord('A'), ord('Z') + 1)]

        # Get unique classes present in both true and predicted
        all_classes = np.unique(np.concatenate([y_true_classes, y_pred_classes]))

        class_report = classification_report(
            y_true_classes, y_pred_classes,
            labels=all_classes,
            target_names=[label_names[i] for i in all_classes],
            output_dict=True,
            zero_division=0
        )
        
        # Create confusion matrix
        cm = confusion_matrix(y_true_classes, y_pred_classes)
        
        evaluation_results = {
            'test_loss': test_loss,
            'test_accuracy': test_accuracy,
            'test_top3_accuracy': test_top3_accuracy,
            'classification_report': class_report,
            'confusion_matrix': cm.tolist(),
            'label_names': label_names
        }
        
        # Print results
        print(f"\nTest Results:")
        print(f"Test Loss: {test_loss:.4f}")
        print(f"Test Accuracy: {test_accuracy:.4f}")
        print(f"Test Top-3 Accuracy: {test_top3_accuracy:.4f}")
        
        return evaluation_results
    
    def plot_training_history(self, save_path: str = None):
        """
        Plot training history
        
        Args:
            save_path: Path to save the plot
        """
        if self.training_history is None:
            print("No training history available")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Plot training & validation accuracy
        axes[0, 0].plot(self.training_history.history['accuracy'], label='Training Accuracy')
        axes[0, 0].plot(self.training_history.history['val_accuracy'], label='Validation Accuracy')
        axes[0, 0].set_title('Model Accuracy')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Plot training & validation loss
        axes[0, 1].plot(self.training_history.history['loss'], label='Training Loss')
        axes[0, 1].plot(self.training_history.history['val_loss'], label='Validation Loss')
        axes[0, 1].set_title('Model Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Plot top-3 accuracy
        axes[1, 0].plot(self.training_history.history['top_3_accuracy'], label='Training Top-3 Accuracy')
        axes[1, 0].plot(self.training_history.history['val_top_3_accuracy'], label='Validation Top-3 Accuracy')
        axes[1, 0].set_title('Model Top-3 Accuracy')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Top-3 Accuracy')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # Plot learning rate (if available)
        if 'lr' in self.training_history.history:
            axes[1, 1].plot(self.training_history.history['lr'], label='Learning Rate')
            axes[1, 1].set_title('Learning Rate')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Learning Rate')
            axes[1, 1].set_yscale('log')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        else:
            axes[1, 1].text(0.5, 0.5, 'Learning Rate\nNot Available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Training history plot saved to {save_path}")
        
        plt.show()
    
    def plot_confusion_matrix(self, evaluation_results: Dict, save_path: str = None):
        """
        Plot confusion matrix
        
        Args:
            evaluation_results: Results from evaluate_model()
            save_path: Path to save the plot
        """
        cm = np.array(evaluation_results['confusion_matrix'])
        label_names = evaluation_results['label_names']
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=label_names, yticklabels=label_names)
        plt.title('Confusion Matrix - ASL Letter Recognition')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Confusion matrix plot saved to {save_path}")
        
        plt.show()
    
    def _save_training_metadata(self, model_name: str, training_time: float, 
                              epochs: int, batch_size: int):
        """Save training metadata"""
        metadata = {
            'model_name': model_name,
            'training_time_seconds': training_time,
            'epochs': epochs,
            'batch_size': batch_size,
            'timestamp': datetime.now().isoformat(),
            'dataset_info': {
                'train_samples': len(self.dataset['X_train']),
                'val_samples': len(self.dataset['X_val']),
                'test_samples': len(self.dataset['X_test']),
                'num_classes': self.dataset['num_classes']
            }
        }
        
        # Save training history
        if self.training_history:
            metadata['training_history'] = {
                key: [float(val) for val in values] 
                for key, values in self.training_history.history.items()
            }
        
        metadata_path = os.path.join(self.model_dir, f"{model_name}_metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"Training metadata saved to {metadata_path}")


if __name__ == "__main__":
    # Example usage
    trainer = ASLTrainer()
    
    # Prepare data
    dataset = trainer.prepare_data(augment=True, augmentation_factor=2)
    
    # Create and train model
    model = trainer.create_model(model_type='custom')
    history = trainer.train_model(epochs=30, batch_size=32)
    
    # Evaluate model
    results = trainer.evaluate_model()
    
    # Plot results
    trainer.plot_training_history()
    trainer.plot_confusion_matrix(results)
