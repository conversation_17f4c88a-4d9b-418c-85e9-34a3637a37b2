"""
Utility functions for ASL Sign Language Recognition System
Common helper functions and utilities
"""

import os
import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import pickle
from datetime import datetime


def create_directory_structure(base_dir: str = "."):
    """
    Create the required directory structure for the project
    
    Args:
        base_dir: Base directory for the project
    """
    directories = [
        "data/raw",
        "data/processed", 
        "data/models",
        "logs",
        "src"
    ]
    
    for directory in directories:
        full_path = os.path.join(base_dir, directory)
        os.makedirs(full_path, exist_ok=True)
        print(f"Created directory: {full_path}")


def get_asl_alphabet() -> List[str]:
    """
    Get list of ASL alphabet letters
    
    Returns:
        List of ASL alphabet letters A-Z
    """
    return [chr(i) for i in range(ord('A'), ord('Z') + 1)]


def load_dataset_info(processed_dir: str = "data/processed") -> Dict:
    """
    Load dataset information from processed data directory
    
    Args:
        processed_dir: Directory containing processed data
        
    Returns:
        Dataset information dictionary
    """
    info_path = os.path.join(processed_dir, "dataset_info.json")
    
    if not os.path.exists(info_path):
        raise FileNotFoundError(f"Dataset info not found: {info_path}")
    
    with open(info_path, 'r') as f:
        return json.load(f)


def load_label_encoder(processed_dir: str = "data/processed"):
    """
    Load label encoder from processed data directory
    
    Args:
        processed_dir: Directory containing processed data
        
    Returns:
        Loaded label encoder
    """
    encoder_path = os.path.join(processed_dir, "label_encoder.pkl")
    
    if not os.path.exists(encoder_path):
        raise FileNotFoundError(f"Label encoder not found: {encoder_path}")
    
    with open(encoder_path, 'rb') as f:
        return pickle.load(f)


def get_available_models(model_dir: str = "data/models") -> List[Dict]:
    """
    Get list of available trained models
    
    Args:
        model_dir: Directory containing saved models
        
    Returns:
        List of model information dictionaries
    """
    if not os.path.exists(model_dir):
        return []
    
    models = []
    
    for filename in os.listdir(model_dir):
        if filename.endswith('.h5'):
            model_path = os.path.join(model_dir, filename)
            metadata_path = os.path.join(model_dir, filename.replace('.h5', '_metadata.json'))
            
            model_info = {
                'name': filename.replace('.h5', ''),
                'path': model_path,
                'size_mb': os.path.getsize(model_path) / (1024 * 1024),
                'created': datetime.fromtimestamp(os.path.getctime(model_path)).isoformat()
            }
            
            # Load metadata if available
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                    model_info.update(metadata)
            
            models.append(model_info)
    
    # Sort by creation date (newest first)
    models.sort(key=lambda x: x['created'], reverse=True)
    
    return models


def get_collection_summary(raw_data_dir: str = "data/raw") -> Dict[str, int]:
    """
    Get summary of collected training data
    
    Args:
        raw_data_dir: Directory containing raw collected images
        
    Returns:
        Dictionary mapping letters to sample counts
    """
    summary = {}
    asl_labels = get_asl_alphabet()
    
    for label in asl_labels:
        label_dir = os.path.join(raw_data_dir, label)
        if os.path.exists(label_dir):
            count = len([f for f in os.listdir(label_dir) if f.endswith('.jpg')])
            summary[label] = count
        else:
            summary[label] = 0
    
    return summary


def plot_data_distribution(data_summary: Dict[str, int], save_path: str = None):
    """
    Plot distribution of collected training data
    
    Args:
        data_summary: Dictionary mapping letters to sample counts
        save_path: Optional path to save the plot
    """
    letters = list(data_summary.keys())
    counts = list(data_summary.values())
    
    plt.figure(figsize=(15, 6))
    bars = plt.bar(letters, counts, color='skyblue', edgecolor='navy', alpha=0.7)
    
    # Add value labels on bars
    for bar, count in zip(bars, counts):
        if count > 0:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    str(count), ha='center', va='bottom', fontweight='bold')
    
    plt.title('ASL Training Data Distribution', fontsize=16, fontweight='bold')
    plt.xlabel('ASL Letters', fontsize=12)
    plt.ylabel('Number of Samples', fontsize=12)
    plt.grid(axis='y', alpha=0.3)
    
    # Add statistics
    total_samples = sum(counts)
    avg_samples = total_samples / len(letters) if letters else 0
    plt.text(0.02, 0.98, f'Total Samples: {total_samples}\nAverage per Letter: {avg_samples:.1f}',
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Data distribution plot saved to {save_path}")
    
    plt.show()


def validate_camera_access(camera_index: int = 0) -> bool:
    """
    Validate that camera is accessible
    
    Args:
        camera_index: Camera device index
        
    Returns:
        True if camera is accessible, False otherwise
    """
    try:
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            return False
        
        ret, frame = cap.read()
        cap.release()
        
        return ret and frame is not None
    
    except Exception:
        return False


def preprocess_image_for_prediction(image: np.ndarray, target_size: Tuple[int, int] = (224, 224)) -> np.ndarray:
    """
    Preprocess image for model prediction
    
    Args:
        image: Input image
        target_size: Target size for resizing
        
    Returns:
        Preprocessed image ready for prediction
    """
    # Convert BGR to RGB if needed
    if len(image.shape) == 3 and image.shape[2] == 3:
        # Assume BGR format from OpenCV
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Resize image
    image = cv2.resize(image, target_size)
    
    # Normalize pixel values
    image = image.astype(np.float32) / 255.0
    
    return image


def calculate_model_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                          label_names: List[str] = None) -> Dict:
    """
    Calculate comprehensive model evaluation metrics
    
    Args:
        y_true: True labels (one-hot encoded)
        y_pred: Predicted probabilities
        label_names: List of label names
        
    Returns:
        Dictionary containing various metrics
    """
    from sklearn.metrics import accuracy_score, precision_recall_fscore_support, top_k_accuracy_score
    
    # Convert one-hot to class indices
    y_true_classes = np.argmax(y_true, axis=1)
    y_pred_classes = np.argmax(y_pred, axis=1)
    
    # Calculate metrics
    accuracy = accuracy_score(y_true_classes, y_pred_classes)
    precision, recall, f1, support = precision_recall_fscore_support(
        y_true_classes, y_pred_classes, average='weighted'
    )
    
    # Top-k accuracy
    top3_accuracy = top_k_accuracy_score(y_true, y_pred, k=3)
    top5_accuracy = top_k_accuracy_score(y_true, y_pred, k=5)
    
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'top3_accuracy': top3_accuracy,
        'top5_accuracy': top5_accuracy
    }
    
    # Per-class metrics if label names provided
    if label_names:
        per_class_precision, per_class_recall, per_class_f1, _ = precision_recall_fscore_support(
            y_true_classes, y_pred_classes, average=None
        )
        
        metrics['per_class'] = {}
        for i, label in enumerate(label_names):
            metrics['per_class'][label] = {
                'precision': per_class_precision[i],
                'recall': per_class_recall[i],
                'f1_score': per_class_f1[i]
            }
    
    return metrics


def save_experiment_results(results: Dict, experiment_name: str, 
                          results_dir: str = "results"):
    """
    Save experiment results to JSON file
    
    Args:
        results: Results dictionary
        experiment_name: Name of the experiment
        results_dir: Directory to save results
    """
    os.makedirs(results_dir, exist_ok=True)
    
    # Add timestamp
    results['timestamp'] = datetime.now().isoformat()
    results['experiment_name'] = experiment_name
    
    # Save to file
    filename = f"{experiment_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    filepath = os.path.join(results_dir, filename)
    
    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"Experiment results saved to {filepath}")


def print_system_info():
    """Print system information for debugging"""
    import tensorflow as tf
    import cv2
    import mediapipe as mp
    
    print("="*50)
    print("SYSTEM INFORMATION")
    print("="*50)
    print(f"TensorFlow version: {tf.__version__}")
    print(f"OpenCV version: {cv2.__version__}")
    print(f"MediaPipe version: {mp.__version__}")
    print(f"GPU available: {tf.config.list_physical_devices('GPU')}")
    print(f"Camera accessible: {validate_camera_access()}")
    print("="*50)


if __name__ == "__main__":
    # Example usage
    print_system_info()
    
    # Create directory structure
    create_directory_structure()
    
    # Get collection summary
    summary = get_collection_summary()
    print("\nData Collection Summary:")
    for letter, count in summary.items():
        print(f"Letter {letter}: {count} samples")
    
    # Plot data distribution
    plot_data_distribution(summary)
