"""
Utility functions for ASL Sign Language Recognition System
Common helper functions and utilities
"""

import os
import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import pickle
from datetime import datetime


def create_directory_structure(base_dir: str = "."):
    """
    Create the required directory structure for the project
    
    Args:
        base_dir: Base directory for the project
    """
    directories = [
        "data/raw",
        "data/processed", 
        "data/models",
        "logs",
        "src"
    ]
    
    for directory in directories:
        full_path = os.path.join(base_dir, directory)
        os.makedirs(full_path, exist_ok=True)
        print(f"Created directory: {full_path}")


def get_asl_alphabet() -> List[str]:
    """
    Get list of ASL alphabet letters

    Returns:
        List of ASL alphabet letters A-Z
    """
    return [chr(i) for i in range(ord('A'), ord('Z') + 1)]


def get_external_dataset_classes(dataset_path: str) -> List[str]:
    """
    Get list of classes from external dataset directory

    Args:
        dataset_path: Path to external dataset directory

    Returns:
        List of class names found in the dataset
    """
    if not os.path.exists(dataset_path):
        return []

    classes = []
    for item in os.listdir(dataset_path):
        item_path = os.path.join(dataset_path, item)
        if os.path.isdir(item_path) and item != 'asl_dataset':  # Skip nested asl_dataset folder
            classes.append(item)

    return sorted(classes)


def validate_external_dataset(dataset_path: str) -> Dict:
    """
    Validate external dataset structure and contents

    Args:
        dataset_path: Path to external dataset directory

    Returns:
        Dictionary with validation results
    """
    validation_result = {
        'valid': False,
        'total_classes': 0,
        'total_images': 0,
        'classes': {},
        'errors': [],
        'warnings': []
    }

    if not os.path.exists(dataset_path):
        validation_result['errors'].append(f"Dataset path does not exist: {dataset_path}")
        return validation_result

    classes = get_external_dataset_classes(dataset_path)
    validation_result['total_classes'] = len(classes)

    if len(classes) == 0:
        validation_result['errors'].append("No class directories found in dataset")
        return validation_result

    total_images = 0
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}

    for class_name in classes:
        class_path = os.path.join(dataset_path, class_name)
        image_files = []

        for file in os.listdir(class_path):
            file_path = os.path.join(class_path, file)
            if os.path.isfile(file_path):
                _, ext = os.path.splitext(file.lower())
                if ext in valid_extensions:
                    image_files.append(file)

        class_count = len(image_files)
        validation_result['classes'][class_name] = class_count
        total_images += class_count

        if class_count == 0:
            validation_result['warnings'].append(f"No images found in class '{class_name}'")
        elif class_count < 10:
            validation_result['warnings'].append(f"Very few images ({class_count}) in class '{class_name}'")

    validation_result['total_images'] = total_images

    if total_images == 0:
        validation_result['errors'].append("No valid images found in dataset")
    else:
        validation_result['valid'] = True

    return validation_result


def get_external_dataset_summary(dataset_path: str) -> Dict[str, int]:
    """
    Get summary of external dataset

    Args:
        dataset_path: Path to external dataset directory

    Returns:
        Dictionary mapping class names to image counts
    """
    summary = {}

    if not os.path.exists(dataset_path):
        return summary

    classes = get_external_dataset_classes(dataset_path)
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}

    for class_name in classes:
        class_path = os.path.join(dataset_path, class_name)
        if os.path.isdir(class_path):
            count = 0
            for file in os.listdir(class_path):
                file_path = os.path.join(class_path, file)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file.lower())
                    if ext in valid_extensions:
                        count += 1
            summary[class_name] = count

    return summary


def load_dataset_info(processed_dir: str = "data/processed") -> Dict:
    """
    Load dataset information from processed data directory
    
    Args:
        processed_dir: Directory containing processed data
        
    Returns:
        Dataset information dictionary
    """
    info_path = os.path.join(processed_dir, "dataset_info.json")
    
    if not os.path.exists(info_path):
        raise FileNotFoundError(f"Dataset info not found: {info_path}")
    
    with open(info_path, 'r') as f:
        return json.load(f)


def load_label_encoder(processed_dir: str = "data/processed"):
    """
    Load label encoder from processed data directory
    
    Args:
        processed_dir: Directory containing processed data
        
    Returns:
        Loaded label encoder
    """
    encoder_path = os.path.join(processed_dir, "label_encoder.pkl")
    
    if not os.path.exists(encoder_path):
        raise FileNotFoundError(f"Label encoder not found: {encoder_path}")
    
    with open(encoder_path, 'rb') as f:
        return pickle.load(f)


def get_available_models(model_dir: str = "data/models") -> List[Dict]:
    """
    Get list of available trained models
    
    Args:
        model_dir: Directory containing saved models
        
    Returns:
        List of model information dictionaries
    """
    if not os.path.exists(model_dir):
        return []
    
    models = []
    
    for filename in os.listdir(model_dir):
        if filename.endswith('.h5'):
            model_path = os.path.join(model_dir, filename)
            metadata_path = os.path.join(model_dir, filename.replace('.h5', '_metadata.json'))
            
            model_info = {
                'name': filename.replace('.h5', ''),
                'path': model_path,
                'size_mb': os.path.getsize(model_path) / (1024 * 1024),
                'created': datetime.fromtimestamp(os.path.getctime(model_path)).isoformat()
            }
            
            # Load metadata if available
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                    model_info.update(metadata)
            
            models.append(model_info)
    
    # Sort by creation date (newest first)
    models.sort(key=lambda x: x['created'], reverse=True)
    
    return models


def get_collection_summary(raw_data_dir: str = "data/raw") -> Dict[str, int]:
    """
    Get summary of collected training data

    Args:
        raw_data_dir: Directory containing raw collected images

    Returns:
        Dictionary mapping letters to sample counts
    """
    summary = {}
    asl_labels = get_asl_alphabet()

    for label in asl_labels:
        label_dir = os.path.join(raw_data_dir, label)
        if os.path.exists(label_dir):
            count = len([f for f in os.listdir(label_dir) if f.endswith('.jpg')])
            summary[label] = count
        else:
            summary[label] = 0

    return summary


def get_external_dataset_summary(dataset_path: str) -> Dict[str, int]:
    """
    Get summary of external dataset

    Args:
        dataset_path: Path to external dataset directory

    Returns:
        Dictionary mapping letters to sample counts
    """
    summary = {}
    asl_labels = get_asl_alphabet()

    # Initialize all letters to 0
    for label in asl_labels:
        summary[label] = 0

    if not os.path.exists(dataset_path):
        return summary

    # Check for flat structure with filename prefixes
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
        image_files.extend([f for f in os.listdir(dataset_path) if f.lower().endswith(ext)])

    # Count files with letter prefixes
    for image_file in image_files:
        if '_' in image_file:
            letter_part = image_file.split('_')[0].upper()
        else:
            letter_part = image_file[0].upper()

        if len(letter_part) == 1 and letter_part in asl_labels:
            summary[letter_part] += 1

    # Check for directory structure
    subdirs = [d for d in os.listdir(dataset_path) if os.path.isdir(os.path.join(dataset_path, d))]
    for subdir in subdirs:
        if len(subdir) == 1 and subdir.upper() in asl_labels:
            letter = subdir.upper()
            subdir_path = os.path.join(dataset_path, subdir)
            subdir_images = [f for f in os.listdir(subdir_path)
                           if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            summary[letter] = len(subdir_images)

    return summary


def validate_external_dataset_path(dataset_path: str) -> Tuple[bool, str]:
    """
    Validate external dataset path

    Args:
        dataset_path: Path to external dataset directory

    Returns:
        Tuple of (is_valid, message)
    """
    if not dataset_path:
        return False, "Dataset path is empty"

    if not os.path.exists(dataset_path):
        return False, f"Dataset path does not exist: {dataset_path}"

    if not os.path.isdir(dataset_path):
        return False, f"Dataset path is not a directory: {dataset_path}"

    # Check for image files
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
        image_files.extend([f for f in os.listdir(dataset_path) if f.lower().endswith(ext)])

    # Check for subdirectories with images
    subdirs = [d for d in os.listdir(dataset_path) if os.path.isdir(os.path.join(dataset_path, d))]
    subdir_images = 0
    for subdir in subdirs:
        subdir_path = os.path.join(dataset_path, subdir)
        subdir_files = [f for f in os.listdir(subdir_path)
                       if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
        subdir_images += len(subdir_files)

    total_images = len(image_files) + subdir_images

    if total_images == 0:
        return False, "No image files found in dataset directory"

    return True, f"Valid dataset with {total_images} images found"


def plot_data_distribution(data_summary: Dict[str, int], save_path: str = None):
    """
    Plot distribution of collected training data
    
    Args:
        data_summary: Dictionary mapping letters to sample counts
        save_path: Optional path to save the plot
    """
    letters = list(data_summary.keys())
    counts = list(data_summary.values())
    
    plt.figure(figsize=(15, 6))
    bars = plt.bar(letters, counts, color='skyblue', edgecolor='navy', alpha=0.7)
    
    # Add value labels on bars
    for bar, count in zip(bars, counts):
        if count > 0:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    str(count), ha='center', va='bottom', fontweight='bold')
    
    plt.title('ASL Training Data Distribution', fontsize=16, fontweight='bold')
    plt.xlabel('ASL Letters', fontsize=12)
    plt.ylabel('Number of Samples', fontsize=12)
    plt.grid(axis='y', alpha=0.3)
    
    # Add statistics
    total_samples = sum(counts)
    avg_samples = total_samples / len(letters) if letters else 0
    plt.text(0.02, 0.98, f'Total Samples: {total_samples}\nAverage per Letter: {avg_samples:.1f}',
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Data distribution plot saved to {save_path}")
    
    plt.show()


def find_working_camera() -> int:
    """
    Find the first working camera index

    Returns:
        Camera index if found, -1 if no camera found
    """
    for i in range(10):  # Check indices 0-9 to cover more devices
        try:
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                if ret and frame is not None:
                    return i
        except Exception:
            continue
    return -1


def validate_camera_access(camera_index: int = None) -> bool:
    """
    Validate that camera is accessible

    Args:
        camera_index: Camera device index (None to auto-detect)

    Returns:
        True if camera is accessible, False otherwise
    """
    try:
        if camera_index is None:
            camera_index = find_working_camera()
            if camera_index == -1:
                return False

        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            return False

        ret, frame = cap.read()
        cap.release()

        return ret and frame is not None

    except Exception:
        return False


def get_camera_info() -> Dict:
    """
    Get information about available cameras

    Returns:
        Dictionary with camera information
    """
    camera_info = {
        'working_camera_index': -1,
        'available_cameras': [],
        'camera_accessible': False
    }

    # Check for working cameras
    for i in range(10):  # Check indices 0-9 to cover more devices
        try:
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    camera_info['available_cameras'].append({
                        'index': i,
                        'resolution': f"{frame.shape[1]}x{frame.shape[0]}",
                        'working': True
                    })
                    if camera_info['working_camera_index'] == -1:
                        camera_info['working_camera_index'] = i
                        camera_info['camera_accessible'] = True
                else:
                    camera_info['available_cameras'].append({
                        'index': i,
                        'resolution': 'Unknown',
                        'working': False
                    })
                cap.release()
        except Exception:
            continue

    return camera_info


def preprocess_image_for_prediction(image: np.ndarray, target_size: Tuple[int, int] = (224, 224)) -> np.ndarray:
    """
    Preprocess image for model prediction
    
    Args:
        image: Input image
        target_size: Target size for resizing
        
    Returns:
        Preprocessed image ready for prediction
    """
    # Convert BGR to RGB if needed
    if len(image.shape) == 3 and image.shape[2] == 3:
        # Assume BGR format from OpenCV
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Resize image
    image = cv2.resize(image, target_size)
    
    # Normalize pixel values
    image = image.astype(np.float32) / 255.0
    
    return image


def calculate_model_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                          label_names: List[str] = None) -> Dict:
    """
    Calculate comprehensive model evaluation metrics
    
    Args:
        y_true: True labels (one-hot encoded)
        y_pred: Predicted probabilities
        label_names: List of label names
        
    Returns:
        Dictionary containing various metrics
    """
    from sklearn.metrics import accuracy_score, precision_recall_fscore_support, top_k_accuracy_score
    
    # Convert one-hot to class indices
    y_true_classes = np.argmax(y_true, axis=1)
    y_pred_classes = np.argmax(y_pred, axis=1)
    
    # Calculate metrics
    accuracy = accuracy_score(y_true_classes, y_pred_classes)
    precision, recall, f1, support = precision_recall_fscore_support(
        y_true_classes, y_pred_classes, average='weighted'
    )
    
    # Top-k accuracy
    top3_accuracy = top_k_accuracy_score(y_true, y_pred, k=3)
    top5_accuracy = top_k_accuracy_score(y_true, y_pred, k=5)
    
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'top3_accuracy': top3_accuracy,
        'top5_accuracy': top5_accuracy
    }
    
    # Per-class metrics if label names provided
    if label_names:
        per_class_precision, per_class_recall, per_class_f1, _ = precision_recall_fscore_support(
            y_true_classes, y_pred_classes, average=None
        )
        
        metrics['per_class'] = {}
        for i, label in enumerate(label_names):
            metrics['per_class'][label] = {
                'precision': per_class_precision[i],
                'recall': per_class_recall[i],
                'f1_score': per_class_f1[i]
            }
    
    return metrics


def save_experiment_results(results: Dict, experiment_name: str, 
                          results_dir: str = "results"):
    """
    Save experiment results to JSON file
    
    Args:
        results: Results dictionary
        experiment_name: Name of the experiment
        results_dir: Directory to save results
    """
    os.makedirs(results_dir, exist_ok=True)
    
    # Add timestamp
    results['timestamp'] = datetime.now().isoformat()
    results['experiment_name'] = experiment_name
    
    # Save to file
    filename = f"{experiment_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    filepath = os.path.join(results_dir, filename)
    
    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"Experiment results saved to {filepath}")


def print_system_info():
    """Print system information for debugging"""
    import tensorflow as tf
    import cv2
    import mediapipe as mp
    
    print("="*50)
    print("SYSTEM INFORMATION")
    print("="*50)
    print(f"TensorFlow version: {tf.__version__}")
    print(f"OpenCV version: {cv2.__version__}")
    print(f"MediaPipe version: {mp.__version__}")
    print(f"GPU available: {tf.config.list_physical_devices('GPU')}")
    print(f"Camera accessible: {validate_camera_access()}")
    print("="*50)


if __name__ == "__main__":
    # Example usage
    print_system_info()
    
    # Create directory structure
    create_directory_structure()
    
    # Get collection summary
    summary = get_collection_summary()
    print("\nData Collection Summary:")
    for letter, count in summary.items():
        print(f"Letter {letter}: {count} samples")
    
    # Plot data distribution
    plot_data_distribution(summary)
