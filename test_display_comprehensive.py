#!/usr/bin/env python3
"""
Comprehensive Display Testing for ASL Recognition System
Tests OpenCV window display, camera access, and provides fallback guidance
"""

import sys
import os
import json
import time
from typing import Dict, List, Tuple

# Add project path
sys.path.append('/home/<USER>/Documents/Sign Language Detector')

def print_header(title: str):
    """Print formatted header"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_section(title: str):
    """Print formatted section"""
    print(f"\n📋 {title}")
    print("-" * 40)

def test_environment_detection():
    """Test environment detection and display diagnostics"""
    print_header("ENVIRONMENT DETECTION & DISPLAY DIAGNOSTICS")
    
    try:
        from src.display_diagnostics import run_display_diagnostics
        
        print("Running comprehensive display diagnostics...")
        results = run_display_diagnostics()
        
        # Display summary
        env = results['environment']
        print_section("Environment Summary")
        print(f"Display Server: {env['display_server']}")
        print(f"Session Type: {env['session_type'] or 'Unknown'}")
        print(f"DISPLAY Variable: {env['display_variable'] or 'Not set'}")
        print(f"OpenCV GUI Support: {'✅ Available' if env['opencv_gui_support'] else '❌ Not available'}")
        print(f"Overall Status: {results['overall_status'].upper()}")
        
        # Window test results
        print_section("Window Test Results")
        for test in results['window_tests']:
            status = "✅ PASS" if test['success'] else "❌ FAIL"
            print(f"{test['test_name']}: {status}")
            if not test['success'] and test['error_message']:
                print(f"  Error: {test['error_message']}")
        
        # Camera test results
        print_section("Camera Test Results")
        for test in results['camera_tests']:
            status = "✅ PASS" if test['success'] else "❌ FAIL"
            print(f"{test['test_name']}: {status}")
            if not test['success'] and test['error_message']:
                print(f"  Error: {test['error_message']}")
        
        return results
        
    except Exception as e:
        print(f"❌ Error running diagnostics: {e}")
        return None

def test_camera_manager():
    """Test camera manager functionality"""
    print_header("CAMERA MANAGER TESTING")
    
    try:
        from src.camera_manager import get_camera_manager
        
        camera_manager = get_camera_manager()
        camera_info = camera_manager.get_camera_info()
        
        print_section("Camera Manager Status")
        print(f"Active Camera: {camera_info.get('active_camera_index', 'None')}")
        print(f"Camera Accessible: {camera_info.get('camera_accessible', False)}")
        print(f"Working Camera Count: {camera_info.get('working_camera_count', 0)}")
        print(f"Config File: {camera_info.get('config_file', 'N/A')}")
        
        print_section("Available Cameras")
        for cam in camera_info.get('available_cameras', []):
            status = "✅ Working" if cam.get('working', False) else "❌ Not working"
            print(f"Camera {cam['index']}: {cam['resolution']} - {status}")
        
        return camera_info
        
    except Exception as e:
        print(f"❌ Error testing camera manager: {e}")
        return None

def test_opencv_display():
    """Test OpenCV display functionality"""
    print_header("OPENCV DISPLAY TESTING")
    
    try:
        import cv2
        import numpy as np
        
        print_section("Basic Window Test")
        
        # Test 1: Basic window creation
        print("Testing basic window creation...")
        try:
            cv2.namedWindow('test_window', cv2.WINDOW_NORMAL)
            test_img = np.zeros((300, 400, 3), dtype=np.uint8)
            test_img[:] = (50, 100, 150)
            cv2.putText(test_img, 'Display Test', (100, 150), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.imshow('test_window', test_img)
            cv2.waitKey(1000)  # 1 second display
            cv2.destroyWindow('test_window')
            print("✅ Basic window test passed")
            return True
        except Exception as e:
            print(f"❌ Basic window test failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ OpenCV import failed: {e}")
        return False

def test_camera_display():
    """Test camera display functionality"""
    print_header("CAMERA DISPLAY TESTING")
    
    try:
        from src.camera_manager import get_camera_manager
        import cv2
        
        camera_manager = get_camera_manager()
        camera_index = camera_manager.get_active_camera()
        
        if camera_index is None:
            print("❌ No active camera available")
            return False
        
        print_section(f"Testing Camera {camera_index}")
        
        # Test camera access
        print("Testing camera access...")
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            print(f"❌ Cannot open camera {camera_index}")
            return False
        
        # Test frame capture
        ret, frame = cap.read()
        if not ret:
            print("❌ Cannot capture frames")
            cap.release()
            return False
        
        print(f"✅ Camera accessible ({frame.shape})")
        
        # Test display
        print("Testing camera display (3 seconds)...")
        try:
            cv2.namedWindow('camera_test', cv2.WINDOW_NORMAL)
            
            start_time = time.time()
            frame_count = 0
            
            while time.time() - start_time < 3.0:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                cv2.putText(frame, f'Frame {frame_count}', (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(frame, 'Camera Display Test', (10, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                cv2.imshow('camera_test', frame)
                cv2.waitKey(30)
            
            cv2.destroyWindow('camera_test')
            cap.release()
            
            print(f"✅ Camera display test passed ({frame_count} frames)")
            return True
            
        except Exception as e:
            print(f"❌ Camera display test failed: {e}")
            cap.release()
            return False
            
    except Exception as e:
        print(f"❌ Camera display test error: {e}")
        return False

def test_inference_engine():
    """Test inference engine display capability"""
    print_header("INFERENCE ENGINE TESTING")
    
    try:
        from src.inference import ASLInferenceEngine
        
        # Check if model exists
        model_path = 'data/models/external_dataset_demo.h5'
        if not os.path.exists(model_path):
            print(f"❌ Model not found: {model_path}")
            print("💡 Train a model first using the Streamlit interface")
            return False
        
        print_section("Inference Engine Display Test")
        
        # Initialize engine
        print("Initializing inference engine...")
        engine = ASLInferenceEngine(model_path)
        
        # Test display capability
        print("Testing display capability...")
        display_available = engine.test_display_capability()
        
        if display_available:
            print("✅ Display capability available")
            print("💡 You can use: engine.run_inference()")
        else:
            print("❌ Display capability not available")
            print("💡 Use Streamlit web interface instead")
        
        return display_available
        
    except Exception as e:
        print(f"❌ Inference engine test failed: {e}")
        return False

def provide_recommendations(test_results: Dict):
    """Provide recommendations based on test results"""
    print_header("RECOMMENDATIONS & SOLUTIONS")
    
    opencv_works = test_results.get('opencv_display', False)
    camera_works = test_results.get('camera_display', False)
    inference_works = test_results.get('inference_engine', False)
    
    if opencv_works and camera_works and inference_works:
        print_section("✅ ALL SYSTEMS WORKING")
        print("Your system supports full OpenCV window display functionality!")
        print("\n🚀 Available options:")
        print("1. Direct inference: python3 demo_enhanced_features.py")
        print("2. Enhanced interface: Use inference engine directly")
        print("3. Web interface: http://localhost:8507 (always available)")
        
    elif camera_works and not opencv_works:
        print_section("⚠️  PARTIAL FUNCTIONALITY")
        print("Camera works but OpenCV display has issues.")
        print("\n💡 Recommended solutions:")
        print("1. ✅ Use Streamlit web interface: http://localhost:8507")
        print("2. For remote access: ssh -X username@hostname")
        print("3. For Wayland: export QT_QPA_PLATFORM=xcb")
        
    else:
        print_section("❌ DISPLAY ISSUES DETECTED")
        print("OpenCV window display not available in this environment.")
        print("\n🌐 Recommended approach:")
        print("1. ✅ Use Streamlit web interface (most reliable)")
        print("2. Web interface provides full camera access and functionality")
        print("3. No display server dependencies")
        
        # Environment-specific guidance
        import os
        session_type = os.environ.get('XDG_SESSION_TYPE')
        display_var = os.environ.get('DISPLAY')
        
        if session_type == 'wayland':
            print("\n🔧 Wayland-specific solutions:")
            print("   • Switch to X11 session")
            print("   • export QT_QPA_PLATFORM=xcb")
            print("   • Use web interface (recommended)")
        
        if not display_var:
            print("\n🔧 Headless/Remote environment:")
            print("   • Use web interface for camera access")
            print("   • For SSH: ssh -X username@hostname")
            print("   • Consider VNC for full desktop")

def main():
    """Main testing function"""
    print("🚀 Comprehensive Display Testing for ASL Recognition System")
    print("This tool tests OpenCV window display, camera access, and provides guidance")
    
    test_results = {}
    
    # Run all tests
    diag_results = test_environment_detection()
    test_results['diagnostics'] = diag_results
    
    camera_info = test_camera_manager()
    test_results['camera_manager'] = camera_info
    
    test_results['opencv_display'] = test_opencv_display()
    test_results['camera_display'] = test_camera_display()
    test_results['inference_engine'] = test_inference_engine()
    
    # Provide recommendations
    provide_recommendations(test_results)
    
    # Final summary
    print_header("FINAL SUMMARY")
    
    working_features = sum([
        test_results.get('opencv_display', False),
        test_results.get('camera_display', False),
        test_results.get('inference_engine', False)
    ])
    
    if working_features == 3:
        status = "🎉 EXCELLENT - All features working"
    elif working_features >= 2:
        status = "✅ GOOD - Most features working"
    elif working_features >= 1:
        status = "⚠️  LIMITED - Some features working"
    else:
        status = "❌ ISSUES - Use web interface"
    
    print(f"Overall Status: {status}")
    print(f"Working Features: {working_features}/3")
    print("\n💡 Remember: Streamlit web interface is always available as a reliable alternative!")
    print("   Access at: http://localhost:8507")

if __name__ == "__main__":
    main()
