#!/usr/bin/env python3
"""
Comprehensive OpenCV Window Stability Test
Demonstrates the fixes for camera window stability issues
"""

import sys
import os
import time
import cv2
import numpy as np
from pathlib import Path

# Add project path
sys.path.append('/home/<USER>/Documents/Sign Language Detector')

from src.camera_window_manager import get_window_manager
from src.camera_manager import get_camera_manager
from src.inference import ASLIn<PERSON><PERSON>ngine

def print_header(title: str):
    """Print formatted header"""
    print("\n" + "=" * 70)
    print(f"🔧 {title}")
    print("=" * 70)

def print_status(message: str, success: bool):
    """Print status message"""
    status = "✅" if success else "❌"
    print(f"   {status} {message}")

def test_window_persistence():
    """Test window persistence and stability"""
    print_header("WINDOW PERSISTENCE TEST")
    
    with get_window_manager() as wm:
        window_name = "Persistence Test"
        
        # Test 1: Create window
        print("Test 1: Window Creation")
        success = wm.create_window(window_name, 640, 480)
        print_status(f"Window '{window_name}' created", success)
        
        if not success:
            return False
        
        # Test 2: Display frames for extended period
        print("Test 2: Extended Frame Display (10 seconds)")
        print("   Window should remain stable and responsive")
        
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 10.0:
            # Create animated frame
            frame = create_animated_frame(640, 480, frame_count)
            
            # Display frame
            success = wm.show_frame(window_name, frame)
            if not success:
                print_status("Frame display failed", False)
                return False
            
            # Check window responsiveness
            if frame_count % 30 == 0:  # Check every 30 frames
                responsive = wm.is_window_open(window_name)
                if not responsive:
                    print_status("Window became unresponsive", False)
                    return False
            
            # Handle key presses
            key = wm.wait_key(33)  # ~30 FPS
            if key == ord('q'):
                print("   User requested quit")
                break
            
            frame_count += 1
        
        total_time = time.time() - start_time
        fps = frame_count / total_time if total_time > 0 else 0
        
        print(f"   Duration: {total_time:.2f} seconds")
        print(f"   Frames displayed: {frame_count}")
        print(f"   Average FPS: {fps:.2f}")
        print_status("Window remained stable throughout test", True)
        
        return True

def test_window_reopening():
    """Test window reopening capability"""
    print_header("WINDOW REOPENING TEST")
    
    with get_window_manager() as wm:
        window_name = "Reopening Test"
        
        # Test multiple open/close cycles
        for cycle in range(3):
            print(f"Cycle {cycle + 1}: Opening window")
            
            # Create window
            success = wm.create_window(f"{window_name} - Cycle {cycle + 1}", 400, 300)
            print_status(f"Window created (cycle {cycle + 1})", success)
            
            if not success:
                return False
            
            # Display some frames
            for i in range(30):
                frame = create_test_frame(400, 300, f"Cycle {cycle + 1} - Frame {i}")
                wm.show_frame(f"{window_name} - Cycle {cycle + 1}", frame)
                wm.wait_key(33)
            
            # Close window
            success = wm.close_window(f"{window_name} - Cycle {cycle + 1}")
            print_status(f"Window closed (cycle {cycle + 1})", success)
            
            # Small delay between cycles
            time.sleep(0.5)
        
        print_status("Multiple window open/close cycles completed", True)
        return True

def test_multiple_windows():
    """Test multiple simultaneous windows"""
    print_header("MULTIPLE WINDOWS TEST")
    
    with get_window_manager() as wm:
        windows = []
        
        # Create multiple windows
        for i in range(3):
            window_name = f"Multi Window {i + 1}"
            success = wm.create_window(window_name, 320, 240)
            print_status(f"Created {window_name}", success)
            
            if success:
                windows.append(window_name)
        
        if len(windows) == 0:
            print_status("No windows created", False)
            return False
        
        # Display content in all windows simultaneously
        print("Displaying content in all windows for 5 seconds...")
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 5.0:
            for i, window_name in enumerate(windows):
                frame = create_test_frame(320, 240, f"{window_name}\nFrame {frame_count}")
                wm.show_frame(window_name, frame)
            
            wm.wait_key(33)
            frame_count += 1
        
        # Check all windows are still responsive
        all_responsive = True
        for window_name in windows:
            responsive = wm.is_window_open(window_name)
            print_status(f"{window_name} responsive", responsive)
            if not responsive:
                all_responsive = False
        
        print_status("All windows remained responsive", all_responsive)
        return all_responsive

def test_camera_integration():
    """Test camera integration with stable windows"""
    print_header("CAMERA INTEGRATION TEST")
    
    # Check camera availability
    cm = get_camera_manager()
    camera_info = cm.get_camera_info()
    
    if not camera_info['camera_accessible']:
        print_status("No camera available - testing with synthetic frames", True)
        return test_synthetic_camera()
    
    camera_index = camera_info['active_camera_index']
    print(f"Testing with camera {camera_index}")
    
    # Test camera with stable window management
    cap = cv2.VideoCapture(camera_index)
    if not cap.isOpened():
        print_status("Failed to open camera", False)
        return False
    
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    with get_window_manager() as wm:
        window_name = "Camera Integration Test"
        
        # Create window
        success = wm.create_window(window_name, 640, 480)
        print_status("Camera window created", success)
        
        if not success:
            cap.release()
            return False
        
        print("Camera test running for 5 seconds...")
        print("Press 'q' to quit early")
        
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 5.0:
            ret, frame = cap.read()
            if not ret:
                print_status("Failed to read camera frame", False)
                break
            
            # Add overlay
            frame = cv2.flip(frame, 1)  # Mirror effect
            overlay_frame = add_camera_overlay(frame, frame_count, time.time() - start_time)
            
            # Display frame
            success = wm.show_frame(window_name, overlay_frame)
            if not success:
                print_status("Failed to display camera frame", False)
                break
            
            # Handle key presses
            key = wm.wait_key(30)
            if key == ord('q'):
                print("   User requested quit")
                break
            
            frame_count += 1
        
        cap.release()
        
        total_time = time.time() - start_time
        fps = frame_count / total_time if total_time > 0 else 0
        
        print(f"   Camera frames processed: {frame_count}")
        print(f"   Average FPS: {fps:.2f}")
        print_status("Camera integration test completed", True)
        
        return True

def test_synthetic_camera():
    """Test with synthetic camera frames"""
    print("Testing with synthetic camera frames...")
    
    with get_window_manager() as wm:
        window_name = "Synthetic Camera Test"
        
        success = wm.create_window(window_name, 640, 480)
        print_status("Synthetic camera window created", success)
        
        if not success:
            return False
        
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 3.0:
            # Create synthetic frame
            frame = create_synthetic_camera_frame(640, 480, frame_count)
            overlay_frame = add_camera_overlay(frame, frame_count, time.time() - start_time)
            
            success = wm.show_frame(window_name, overlay_frame)
            if not success:
                break
            
            wm.wait_key(33)
            frame_count += 1
        
        print_status("Synthetic camera test completed", True)
        return True

def test_inference_engine_integration():
    """Test inference engine with enhanced window management"""
    print_header("INFERENCE ENGINE INTEGRATION TEST")
    
    try:
        # Load inference engine
        engine = ASLInferenceEngine('data/models/validation_test_model.h5')
        print_status("Inference engine loaded", True)
        
        # Test display capability
        display_ok = engine.test_display_capability()
        print_status("Enhanced display capability test", display_ok)
        
        return display_ok
        
    except Exception as e:
        print_status(f"Inference engine test failed: {e}", False)
        return False

def create_animated_frame(width: int, height: int, frame_number: int) -> np.ndarray:
    """Create animated test frame"""
    frame = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Animated background
    offset = (frame_number * 3) % 255
    for y in range(height):
        color = (y + offset) % 255
        frame[y, :] = [color // 4, color // 2, color]
    
    # Moving elements
    center_x = int(width // 2 + 150 * np.sin(frame_number * 0.05))
    center_y = int(height // 2 + 100 * np.cos(frame_number * 0.05))
    cv2.circle(frame, (center_x, center_y), 40, (255, 255, 255), -1)
    
    # Frame info
    cv2.putText(frame, f'Stability Test - Frame {frame_number}', (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, 'Window should remain stable', (10, height - 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    return frame

def create_test_frame(width: int, height: int, text: str) -> np.ndarray:
    """Create simple test frame with text"""
    frame = np.zeros((height, width, 3), dtype=np.uint8)
    frame[:] = (64, 128, 192)  # Blue background
    
    # Add text
    lines = text.split('\n')
    for i, line in enumerate(lines):
        y = 50 + i * 40
        cv2.putText(frame, line, (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    return frame

def create_synthetic_camera_frame(width: int, height: int, frame_number: int) -> np.ndarray:
    """Create synthetic camera-like frame"""
    frame = np.random.randint(0, 50, (height, width, 3), dtype=np.uint8)
    
    # Add some structure
    cv2.rectangle(frame, (100, 100), (width-100, height-100), (100, 150, 200), 2)
    cv2.circle(frame, (width//2, height//2), 50, (200, 200, 100), -1)
    
    # Add frame number
    cv2.putText(frame, f'Synthetic Frame {frame_number}', (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    return frame

def add_camera_overlay(frame: np.ndarray, frame_count: int, elapsed_time: float) -> np.ndarray:
    """Add overlay to camera frame"""
    overlay = frame.copy()
    h, w = frame.shape[:2]
    
    # Status overlay
    cv2.rectangle(overlay, (10, h-80), (250, h-10), (0, 0, 0), -1)
    cv2.putText(overlay, f"Frame: {frame_count}", (20, h-60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    cv2.putText(overlay, f"Time: {elapsed_time:.1f}s", (20, h-40), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    cv2.putText(overlay, f"FPS: {frame_count/elapsed_time:.1f}" if elapsed_time > 0 else "FPS: --", 
               (20, h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # Stability indicator
    cv2.circle(overlay, (w-30, 30), 15, (0, 255, 0), -1)
    cv2.putText(overlay, "STABLE", (w-80, 40), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    return cv2.addWeighted(overlay, 0.8, frame, 0.2, 0)

def main():
    """Main test function"""
    print("🚀 Comprehensive OpenCV Window Stability Test")
    print("Testing fixes for camera window stability issues")
    
    tests = [
        ("Window Persistence", test_window_persistence),
        ("Window Reopening", test_window_reopening),
        ("Multiple Windows", test_multiple_windows),
        ("Camera Integration", test_camera_integration),
        ("Inference Engine Integration", test_inference_engine_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            success = test_func()
            results[test_name] = success
            print_status(f"{test_name} test completed", success)
        except Exception as e:
            print_status(f"{test_name} test failed: {e}", False)
            results[test_name] = False
    
    # Summary
    print_header("TEST SUMMARY")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    for test_name, success in results.items():
        print_status(test_name, success)
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ OpenCV window stability issues have been resolved")
        print("✅ Camera windows now remain stable and can be reopened")
        print("✅ Enhanced window management is working correctly")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("Some stability issues may still exist")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
